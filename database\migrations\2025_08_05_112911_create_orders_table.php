<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_no', 30)->unique();
            $table->foreignId('app_user_id')->nullable()->constrained('app_users')->onDelete('cascade');
            $table->foreignId('collection_center_id')->nullable()->constrained('collection_centers')->onDelete('cascade');

            $table->decimal('sub_total', 8, 2);
            $table->decimal('grand_total', 8, 2);

            $table->foreignId('coupon_id')->nullable()->constrained('coupons')->nullOnDelete(); //center or user or phlebo
            $table->decimal('user_discount_amt', 8, 2)->nullable();
            $table->decimal('center_discount_amt', 8, 2)->nullable();

            $table->integer('reward_point_used')->nullable();
            $table->decimal('reward_amt_used', 8, 2)->nullable();

            $table->date('collection_date')->nullable();
            $table->foreignId('slot_id')->nullable()->constrained('slots')->nullOrDelete(); // slots table

            $table->enum('booking_type', ['INSTANT', 'LEAD']); // 1 = instant, 2 = lead
            $table->foreignId('app_user_address_id')->nullable()->constrained()->nullOnDelete();

            $table->enum('payment_type', ['ONLINE', 'OFFLINE']);
            $table->string('payment_id', 100)->nullable(); // transaction number
            $table->enum('payment_status', ['PENDING', 'SUCCESS', 'FAILED'])->default('PENDING');

            $table->enum('booking_status', ['CONFIRMED', 'CANCELLED', 'SAMPLE_COLLECTED', 'SENT_TO_LAB', 'COMPLETED'])->default('CONFIRMED');

            $table->timestamp('cancelled_at')->nullable();
            $table->foreignId('cancel_reason_id')->nullable()->constrained('cancel_reasons')->nullOnDelete();
            $table->string('cancel_remark', 200)->nullable();

            $table->string('receipt_no', 100)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
