<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AppUserRewardPoint extends Model
{
    protected $fillable = [
        'app_user_id',
        'points',
        'type',
        'expiry_date',
        'remark',
        'reason'
    ];

    public function appUser()
    {
        return $this->belongsTo(AppUser::class);
    }

    public function getExpiryDateAttribute($value)
    {
        return formatDateToDMY($value);
    }
}
