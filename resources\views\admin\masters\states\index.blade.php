@extends('layouts.admin')

@section('content')
<x-alert />

{{-- Content Section --}}
<div class="container-fluid py-1 min-vh-100">

    {{-- <PERSON> Header Card --}}
    <div class="card border-0 shadow rounded-4 bg-white mb-4">
        <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center py-3 px-4">
            <div>
                <h5 class="mb-0 fw-semibold">States Management</h5>
                <small class="text-muted">Manage states — create, edit states.</small>
            </div>
            <a href="#" class="btn btn-primary d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addStateModal">
                <i class="fa fa-plus me-2"></i> Add State
            </a>
        </div>
    </div>

    {{-- States Table Card --}}
    <div class="card border-0 shadow rounded-4 bg-white">
    {{-- <PERSON> Header with Filter --}}
    <div class="card-header bg-white border-bottom py-3 px-4">
        <form action="{{ route('states.index') }}" method="GET">
            <div class="row g-3 align-items-center">
                <div class="col-md-3">
                    <h5 class="mb-0 fw-semibold">🎯 States List</h5>
                </div>
                <div class="col-md-3">
                    <input type="text" name="name" value="{{ request('name') }}" placeholder="Search by name..." class="form-control form-control-sm">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select form-select-sm">
                        <option value="">All Statuses</option>
                        <option value="active" @selected(request('status') == 'active')>Active</option>
                        <option value="inactive" @selected(request('status') == 'inactive')>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex gap-2">
                    <button type="submit" class="btn btn-sm btn-primary">
                        <i class="fa fa-search me-1"></i> Search
                    </button>
                    <a href="{{ route('states.index') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="fa fa-refresh me-1"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>

    {{-- Table Section --}}
    <div class="card-body p-0">
        <div class="table-responsive bg-light">
            <table class="table table-hover align-middle mb-0" id="stateTable">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" style="width: 5%;">SN.</th>
                        <th class="text-start">Name</th>
                        <th class="text-center" style="width: 15%;">Status</th>
                        <th class="text-center" style="width: 20%;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($records as $row)
                        <tr>
                            <td class="text-center text-muted">{{ $loop->iteration }}</td>
                            <td class="text-start">{{ $row->name }}</td>
                            <td class="text-center">
                                <span class="badge bg-{{ $row->status == 'active' ? 'success' : 'secondary' }}">
                                    {{ ucfirst($row->status) }}
                                </span>
                            </td>
                            <td class="text-center">
                                <a href="#" class="btn btn-sm btn-outline-warning me-1" title="Edit" data-bs-toggle="modal" data-bs-target="#editStateModal{{ $row->id }}">
                                    <i class="fa fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4" class="text-center text-muted py-4">
                                <i class="fa fa-circle-exclamation me-2 text-secondary"></i> No states found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>

            {{-- Pagination --}}
            <div class="mt-3 px-3">
                {{ $records->appends(request()->query())->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>
</div>

</div>

{{-- Add State Modal --}}
<div class="modal fade" id="addStateModal" tabindex="-1" aria-labelledby="addStateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStateModalLabel">Add New State <i class="fa fa-plus"></i></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('states.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">State Name</label>
                        <input type="text" class="form-control" id="roleName" name="name" maxlength="255" value="{{ old('name') }}" required placeholder="Enter state name">
                    </div>  
                    <div class="mb-3">
                        <label for="roleStatus" class="form-label">Status</label>
                        <select class="form-select" id="roleStatus" name="status" required>
                            <option value="active" @selected(old('status') == 'active')>Active</option>
                            <option value="inactive" @selected(old('status') == 'inactive')>Inactive</option>
                        </select>
                    </div>    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-check"></i> Add
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- Edit State Modal --}}
@foreach($records as $row)
<div class="modal fade" id="editStateModal{{ $row->id }}" tabindex="-1" aria-labelledby="editStateModalLabel{{ $row->id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStateModalLabel{{ $row->id }}">Edit State <i class="fa fa-edit"></i></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('states.update', $row->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roleName{{ $row->id }}" class="form-label">State Name</label>
                        <input type="text" class="form-control" id="roleName{{ $row->id }}" name="name" value="{{ $row->name }}" required placeholder="Enter state name">
                    </div>
                    <div class="mb-3">
                        <label for="roleStatus{{ $row->id }}" class="form-label">Status</label>
                        <select class="form-select" id="roleStatus{{ $row->id }}" name="status" required>
                            <option value="active" {{ $row->status == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ $row->status == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>   
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-check"></i> Update
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach

{{-- Search Filter Script --}}
@push('scripts')
    <script>
        document.getElementById('searchInput').addEventListener('keyup', function () {
            const search = this.value.toLowerCase();
            const rows = document.querySelectorAll('#stateTable tbody tr');

            rows.forEach(row => {
                const name = row.children[1].textContent.toLowerCase();
                row.style.display = name.includes(search) ? '' : 'none';
            });
        });
    </script>
@endpush

@endsection