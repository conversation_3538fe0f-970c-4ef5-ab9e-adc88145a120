<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Masters\State;
use App\Models\Masters\City;

class AppUserAddress extends Model
{
    protected $fillable = [
        'app_user_id',
        'name',
        'mobile',
        'house_no',
        'area',
        'landmark',
        'pin_code',
        'geo_location_latitude',
        'geo_location_longitude',
        'state_id',
        'city_id',
        'type',
        'use_for_billing',
        'status',
    ];

    public function appUser(){
        return $this->belongsTo(AppUser::class);
    }

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }
}
