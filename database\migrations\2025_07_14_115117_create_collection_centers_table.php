<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('collection_centers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('center_code',20)->unique();
            $table->string('title')->nullable();
            $table->string('owner_name',100);
            $table->date('dob')->nullable();
            $table->string('gender',20)->nullable();
            $table->foreignId('blood_group_id')->nullable()->constrained('blood_groups')->onDelete('set null');
            $table->string('center_name');
            $table->string('mobile_no',10)->unique();
            $table->string('alternate_mobile_no',10)->nullable();
            $table->string('email',100)->unique();
            $table->string('password',100);
            $table->string('profile',100)->nullable();
            $table->foreignId('state_id')->nullable()->constrained('states')->onDelete('set null');
            $table->foreignId('city_id')->nullable()->constrained('cities')->onDelete('set null');
            $table->string('address');
            $table->string('pincode',6);
            $table->string('latitude',20)->nullable();
            $table->string('longitude',20)->nullable();
            $table->time('opening_time_monday')->nullable();
            $table->time('closing_time_monday')->nullable();
            $table->time('opening_time_tuesday')->nullable();
            $table->time('closing_time_tuesday')->nullable();
            $table->time('opening_time_wednesday')->nullable();
            $table->time('closing_time_wednesday')->nullable();
            $table->time('opening_time_thursday')->nullable();
            $table->time('closing_time_thursday')->nullable();
            $table->time('opening_time_friday')->nullable();
            $table->time('closing_time_friday')->nullable();
            $table->time('opening_time_saturday')->nullable();
            $table->time('closing_time_saturday')->nullable();
            $table->time('opening_time_sunday')->nullable();
            $table->time('closing_time_sunday')->nullable();
            $table->enum('status',['active','inactive'])->default('active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('collection_centers');
    }
};
