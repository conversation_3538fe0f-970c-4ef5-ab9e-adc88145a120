<?php

namespace App\Http\Controllers;

use App\Models\CollectionCenter;
use Illuminate\Http\Request;

class CollectionCenterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CollectionCenter $collectionCenter)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CollectionCenter $collectionCenter)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CollectionCenter $collectionCenter)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CollectionCenter $collectionCenter)
    {
        //
    }
}
