@extends('layouts.admin')

@section('content')
    <x-alert />

    <div class="container-fluid py-1 min-vh-100">

        {{-- Page Header Card (Keep Rounded) --}}
        <div class="card border-0 shadow bg-white mb-4">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center py-3 px-4">
                <div>
                    <h5 class="mb-0 fw-semibold">User Help & Support</h5>
                    <small class="text-muted">Manage the content of the Help & Support section.</small>
                </div>
            </div>
        </div>

        {{-- About Us Form Card (No Rounded Corners) --}}
        <div class="card border-0 shadow bg-white"> {{-- removed rounded-4 --}}
            <div class="card-body p-4">
                <form action="{{ route('settings.updateOrCreateHelpSupportUser') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <div class="mb-3">
                        <label for="value" class="form-label fw-semibold">Help & Support <span class="text-danger">*</span></label>
                        <textarea name="value" id="value" rows="6" class="form-control editor">{{ old('value', $helpSupport ?? '') }}</textarea>
                    </div>

                    <div class="d-flex gap-2 align-items-center mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save me-1"></i> {{ isset($helpSupport) ? 'Update' : 'Create' }}
                        </button>

                        <a href="{{ url()->current() }}" class="btn btn-outline-secondary">
                            <i class="fa fa-rotate-left me-1"></i> Reset
                        </a>
                    </div>

                </form>
            </div>
        </div>

    </div>
@endsection