<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_users', function (Blueprint $table) {
            $table->id();
            $table->string('app_user_id',20)->nullable();
            $table->string('mobile',10)->unique();
            $table->string('otp', 6);
            $table->string('password');
            $table->foreignId('state_id')->nullable()->constrained('states')->onDelete('set null');
            $table->foreignId('city_id')->nullable()->constrained('cities')->onDelete('set null');
            $table->string('latitude',20)->nullable();
            $table->string('longitude',20)->nullable();

            $table->string('name')->nullable();
            $table->string('email',50)->nullable();
            $table->string('profile_image',100)->nullable();
            $table->string('gender', 10)->nullable();
            $table->foreignId('blood_group_id')->nullable()->constrained('blood_groups')->onDelete('set null');
            $table->date('dob')->nullable();
            $table->enum('blood_doner_status',['visible','hidden'])->default('hidden');
            $table->string('address')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_users');
    }
};
