@extends('layouts.admin')

@section('content')
<x-alert />

{{-- Content Section --}}
<div class="container-fluid py-1 min-vh-100">

    {{-- <PERSON> Header Card --}}
    <div class="card border-0 shadow rounded-4 bg-white mb-4">
        <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center py-3 px-4">
            <div>
                <h5 class="mb-0 fw-semibold">Cities Management</h5>
                <small class="text-muted">Manage user cities — create, edit cities.</small>
            </div>
            <a href="#" class="btn btn-primary d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addStateModal">
                <i class="fa fa-plus me-2"></i> Add City
            </a>
        </div>
    </div>

    {{-- Cities Table Card --}}
    <div class="card border-0 shadow rounded-4 bg-white">
        {{-- Card Header with Filter --}}
        <div class="card-header bg-white border-bottom py-3 px-4">
            <form action="{{ route('cities.index') }}" method="GET">
                <div class="row g-3 align-items-center">
                    <div class="col-md-2">
                        <h5 class="mb-0 fw-semibold">🎯 Cities List</h5>
                    </div>
                    <div class="col-md">
                        <input type="text" name="name" value="{{ request('name') }}" placeholder="Search by name..." class="form-control form-control-sm">
                    </div>
                    <div class="col-md">
                        <select name="state_id" class="form-select form-select-sm">
                            <option value="">All States</option>
                            @foreach($allStates as $state)
                                <option value="{{ $state->id }}" @selected(request('state_id') == $state->id)>{{ $state->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Statuses</option>
                            <option value="active" @selected(request('status') == 'active')>Active</option>
                            <option value="inactive" @selected(request('status') == 'inactive')>Inactive</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex gap-2">
                        <button type="submit" class="btn btn-sm btn-primary">
                            <i class="fa fa-search me-1"></i> Search
                        </button>
                        <a href="{{ route('cities.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fa fa-refresh me-1"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>

        {{-- Table Section --}}
        <div class="card-body p-0">
            <div class="table-responsive bg-light">
                <table class="table table-hover align-middle mb-0" id="stateTable">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">SN.</th>
                            <th>Name</th>
                            <th>State</th>
                            <th>Public Status</th>
                            <th>Status</th>
                            <th>New Request</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($records as $row)
                            <tr>
                                <td class="text-center text-muted">{{ $loop->iteration }}</td>
                                <td>{{ $row->name }}</td>
                                <td>
                                    @if($row->state)
                                        {{ $row->state->name }}
                                    @else
                                        <span class="text-secondary">N/A</span>
                                    @endif
                                </td>
                                <td>{{ $row->public_status }}</td>
                                <td>
                                    <span class="badge bg-{{ $row->status == 'active' ? 'success' : 'secondary' }}">
                                        {{ ucfirst($row->status) }}
                                    </span>
                                </td>
                                <td>{{ $row->new_request }}</td>
                                <td class="text-center">
                                    <a href="#" class="btn btn-sm btn-outline-warning me-1" title="Edit" data-bs-toggle="modal" data-bs-target="#editCityModal{{ $row->id }}">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="fa fa-circle-exclamation me-2 text-secondary"></i> No cities found.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

                {{-- Pagination --}}
                <div class="mt-3 px-3">
                    {{ $records->appends(request()->query())->links('pagination::bootstrap-5') }}
                </div>
            </div>
        </div>
    </div>

</div>

{{-- Add City Modal --}}
<div class="modal fade" id="addStateModal" tabindex="-1" aria-labelledby="addStateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStateModalLabel">Add New City <i class="fa fa-plus"></i></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('cities.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">City Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="roleName" name="name" maxlength="255" value="{{ old('name') }}" required placeholder="Enter role name">
                    </div>  
                    <div class="mb-3">
                        <label for="stateId" class="form-label">State <span class="text-danger">*</span></label>
                        <select class="form-select" id="stateId" name="state_id" required>
                            <option value="">Select State</option>
                            @foreach($states as $state)
                                <option value="{{ $state->id }}" @selected(old('state_id') == $state->id)>{{ $state->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="publicStatus" class="form-label">Public Status</label>
                        <input type="number" class="form-control" id="publicStatus" name="public_status" max="9" value="{{ old('public_status', 0) }}">
                    </div>
                    <div class="mb-3">
                        <label for="roleStatus" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="roleStatus" name="status" required>
                            <option value="active" @selected(old('status') == 'active')>Active</option>
                            <option value="inactive" @selected(old('status') == 'inactive')>Inactive</option>
                        </select>
                    </div>  
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-check"></i> Add City
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- Edit City Modal --}}
@foreach($records as $row)
<div class="modal fade" id="editCityModal{{ $row->id }}" tabindex="-1" aria-labelledby="editCityModalLabel{{ $row->id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCityModalLabel{{ $row->id }}">Edit City <i class="fa fa-edit    "></i></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('cities.update', $row->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roleName{{ $row->id }}" class="form-label">City Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="roleName{{ $row->id }}" name="name" value="{{ $row->name }}" required placeholder="Enter role name">
                    </div>
                    <div class="mb-3">
                        <label for="stateId{{ $row->id }}" class="form-label">State <span class="text-danger">*</span></label>
                        <select class="form-select" id="stateId{{ $row->id }}" name="state_id" required>
                            <option value="">Select State</option>
                            @foreach($states as $state)
                                <option value="{{ $state->id }}" @selected($row->state_id == $state->id)>{{ $state->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="publicStatus{{ $row->id }}" class="form-label">Public Status</label>
                        <input type="number" class="form-control" id="publicStatus{{ $row->id }}" name="public_status" max="9" value="{{ $row->public_status }}" nullable>  
                    </div>
                    <div class="mb-3">
                        <label for="roleStatus{{ $row->id }}" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="roleStatus{{ $row->id }}" name="status" required>
                            <option value="active" {{ $row->status == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ $row->status == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>   
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-check"></i> Update City
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach

{{-- Search Filter Script --}}
@push('scripts')
    <script>
        document.getElementById('searchInput').addEventListener('keyup', function () {
            const search = this.value.toLowerCase();
            const rows = document.querySelectorAll('#stateTable tbody tr');

            rows.forEach(row => {
                const name = row.children[1].textContent.toLowerCase();
                row.style.display = name.includes(search) ? '' : 'none';
            });
        });
    </script>
@endpush

@endsection