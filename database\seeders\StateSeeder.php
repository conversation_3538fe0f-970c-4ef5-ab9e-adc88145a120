<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use DB;

class StateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (DB::table('states')->count() > 0) {
            return; // Skip seeding if states already exist
        }

        \DB::table('states')->insert([
            ['name' => 'Andhra Pradesh', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Arunachal Pradesh', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Assam', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Bihar', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Chhattisgarh', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Goa', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Gujarat', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Haryana', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Himachal Pradesh', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Jharkhand', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Karnataka', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Kerala', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Madhya Pradesh', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Maharashtra', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => "Manipur", "status" =>'inactive','created_at'=>now(),'updated_at'=>now()],
            ['name' => 'Meghalaya', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Mizoram', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Nagaland', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Odisha', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Punjab', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Rajasthan', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Sikkim', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Tamil Nadu', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => "Telangana", "status" =>'inactive','created_at'=>now(),'updated_at'=>now()],
            ['name' => "Tripura", "status" =>'inactive','created_at'=>now(),'updated_at'=>now()],
            ['name' => "Uttar Pradesh", "status" =>'inactive','created_at'=>now(),'updated_at'=>now()],
            ['name' => "Uttarakhand", "status" =>'inactive','created_at'=>now(),'updated_at'=>now()],
            ['name' => "West Bengal", "status" =>'inactive','created_at'=>now(),'updated_at'=>now()],
            ['name' => 'Andaman and Nicobar Islands', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Chandigarh', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Dadra and Nagar Haveli and Daman and Diu', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Lakshadweep', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Delhi', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Puducherry', 'status' => 'inactive', 'created_at' => now(), 'updated_at' => now()],
        ]);
    }
}
