<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CollectionCenter;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class AuthCollectionCenterController extends Controller
{
    public function login(Request $request){
        $validated = Validator::make($request->all(),[
            'email' => 'required|email|max:100|exists:collection_centers',
            'password' => 'required|max:20|string',
        ]);
        if ($validated->fails()) {
            return response()->json(['status' => false, 'message'=>'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        $collectionCenter = CollectionCenter::where('email',$request->email)->first();
        if($collectionCenter->status == "inactive"){
            return response()->json(['status' => false, 'message'=>'Your account is inactive please contact as admin!!', 'error' => 'Your account is inactive please contact as admin!'], 422);
        }
        if(!(Hash::check($request->password, $collectionCenter->password))){
            return response()->json(['status' => false, 'message'=>'Your Password Are Not match!', 'error' => 'Your Password Are Not match!'], 422);
        }

        $data = $collectionCenter;
        $data->token =  $collectionCenter->createToken('app-user-token')->plainTextToken;
        return response()->json(['status' => true, 'message' => 'Login Successfully', 'data' => $data], 200);
    }

    public function profile(Request $request){
        $collectionCenter = $request->user();
        $collectionCenter['dob'] = formatDateToDMY($collectionCenter->dob);
        $collectionCenter['profile_image_url'] =  $collectionCenter->profile_image_url;
        return response()->json(['status' => true, 'message' => 'Profile Fetched Successfully', 'data' => $collectionCenter], 200);
    }

    public function profileImageUpdate(Request $request){
        $validated = Validator::make($request->all(), [
            'profile' => 'required|mimes:jpg,png,jpeg,webp|max:2048'
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        try {
            $collectionCenter = CollectionCenter::find($request->user()->id);
            $oldImageProfile = $collectionCenter->profile;
            $profileImage = date('dmYHis') . rand(111111,999999)  . '.' . $request->profile->getClientOriginalExtension();
            $request->file('profile')->move(public_path('uploads/collection-centers/'),$profileImage);

            if(file_exists(public_path('uploads/collection-centers/'.$oldImageProfile)) && !empty($oldImageProfile)){
                unlink(public_path('uploads/collection-centers/'.$oldImageProfile));
            }

            CollectionCenter::where('id',$collectionCenter->id)->update(['profile' => $profileImage]);
            return response()->json(['status' => true, 'message' => 'Profile Updated Successfully', 'data' => $collectionCenter], 200);
        } catch (\Exception $ex) {
            Log::error('collection center image update api error : ' . $ex->getMessage());
            return response()->json(['status' => false,'message' => 'Something went wrong!','error' => $ex->getMessage()], 500);
        }
    }

    public function profileUpdate(Request $request){
        $validated = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'owner_name' => 'required|string|max:255',
            'dob' => 'required|string|date',
            'gender' => 'required|string|max:20',
            'blood_group_id' => 'required|exists:blood_groups,id',
            'email' => 'required|email|max:100',
            'mobile_no' => 'required|numeric|digits:10',
            'center_name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'pincode' => 'required|numeric|digits:6',
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        try {
            CollectionCenter::where('id',$request->user()->id)->update([
                'title' => sanitizeForCSV($request->title),
                'owner_name' => sanitizeForCSV($request->owner_name),
                'dob' => date('Y-m-d', strtotime($request->dob)),
                'gender' => sanitizeForCSV($request->gender),
                'blood_group_id' => $request->blood_group_id,
                'email' => $request->email,
                'mobile_no' => $request->mobile_no,
                'center_name' => sanitizeForCSV($request->center_name),
                'address' => sanitizeForCSV($request->address),
                'pincode' => $request->pincode,
                'state_id' => $request->state_id,
                'city_id' => $request->city_id
            ]);
            return response()->json(['status' => true, 'message' => 'Profile Updated Successfully'], 200);
        } catch (\Exception $ex) {
            Log::error('collection center update api error : ' . $ex->getMessage());
            return response()->json(['status' => false,'message' => 'Something went wrong!','error' => $ex->getMessage()], 500);
        }
    }

    public function changePassword(Request $request){
        $validated = Validator::make($request->all(),[
            'old_password' => 'required',
            'password' => 'required|string|max:20|min:6|confirmed'
        ]);
        if($validated->fails()){
            return response()->json(['status'=>false, 'message'=>'Validation error!', 'error'=>$validated->errors()->first()], 422);
        }

        try {
            $collectionCenter = CollectionCenter::where('id',$request->user()->id)->first();
            if(Hash::check($request->old_password, $collectionCenter->password)){
                CollectionCenter::where('id',$request->user()->id)->update([
                    'password' => Hash::make($request->password)
                ]);
            }else{
                return response()->json(['status'=>false, 'message'=>'Old Password Are Not Match!', 'error'=>'Old Password Are Not Match!'], 422);
            }
            return response()->json(['status'=>true, 'message'=>'Password Change Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('password change error: ' . $ex->getMessage());
            return response()->json(['status'=>false, 'message'=>'Something Went Wrong!', 'error'=>$ex->getMessage()],500);
        }
    }

    public function logout(Request $request){
        $request->user()->currentAccessToken()->delete();
        return response()->json(['status' => true, 'message' => 'Logged out successfully'], 200);
    }
}
