<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('constant.app_name') }} - Dashboard</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ asset('assets/admin/bootstrap/bootstrap.min.css') }}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" />

    <!-- jQuery -->
    <script src="{{ asset('assets/jquery/jquery.min.js') }}"></script>

    <link rel="icon" href="{{ asset('logo-img.png') }}" type="image/png">

    <style>
        html, body {
            height: 100%;
            margin: 0;
        }

        .wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        main {
            flex: 1;
        }

        /* Sidebar */
        #sidebarMenu {
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            height: 100vh;
            background: #fff;
            border-right: 1px solid #ddd;
            z-index: 1040;
            transition: transform 0.3s ease-in-out;
        }

        /* Content */
        .main-content {
            margin-left: 250px;
            transition: all 0.3s ease-in-out;
            padding: 00px;
        }

        /* Collapsed */
        #sidebarMenu.collapsed {
            transform: translateX(-250px);
        }

        .main-content.collapsed {
            margin-left: 0;
        }

        /* Mobile */
        @media (max-width: 767.98px) {
            #sidebarMenu {
                transform: translateX(-250px);
            }

            #sidebarMenu.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle-btn {
                display: inline-block !important;
            }
        }

        .sidebar-toggle-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        #sidebarMenu .nav-link {
            padding: 10px 15px;
            border-radius: 8px;
            transition: background 0.3s, color 0.3s;
            color: #333;
        }

        #sidebarMenu .nav-link:hover {
            background-color: #f8f9fa;
            color: #007bff;
        }

        #sidebarMenu .nav-link.active {
            background-color: #e9ecef;
            font-weight: bold;
            color: #0d6efd;
        }

        /* sidebar scroll */
        .sidebar-scroll {
            max-height: calc(100vh - 70px); /* 70px = approx. height of logo + padding */
            overflow-y: auto;
            scrollbar-width: thin; /* Firefox */
        }

        /* Optional scrollbar styling for WebKit browsers (Chrome/Safari) */
        .sidebar-scroll::-webkit-scrollbar {
            width: 6px;
        }
        .sidebar-scroll::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }
    </style>

    @yield('styles')

    <script src="{{ asset('assets/sweetalert/sweetalert2.min.js') }}"></script>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top py-2">
        <div class="container-fluid px-3">
            <!-- Sidebar Toggle (Visible on Mobile Only) -->
            <button class="btn btn-outline-secondary d-lg-none me-2" id="sidebarToggle" type="button" aria-label="Toggle sidebar">
                <i class="fa fa-bars"></i>
            </button>

            <!-- Logo & App Name -->
            <a class="navbar-brand d-flex align-items-center fw-semibold" href="/">
                <img src="{{ asset('logo-img.png') }}" alt="Logo" width="40" class="me-2">
                {{ config('constant.app_name') }}
            </a>

            <!-- Right Side Icons (Optional) -->
            <ul class="navbar-nav ms-auto d-flex flex-row align-items-center gap-3">
                <li class="nav-item">
                    <a class="nav-link text-dark" href="#"><i class="fa-solid fa-cart-shopping"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-dark" href="#"><i class="fa-solid fa-phone-volume"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-dark" href="#"><i class="fa-solid fa-message"></i></a>
                </li>

                <!-- Profile Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="https://github.com/mdo.png" alt="Profile" class="rounded-circle" width="32" height="32">
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#">Profile</a></li>
                        <li><a class="dropdown-item" href="#">Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ route('logout') }}">Logout</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm" id="sidebarMenu">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <a href="/" class="d-flex align-items-center text-decoration-none text-dark">
                <img src="{{ asset('logo-img.png') }}" alt="Logo" width="40" class="me-2">
                <span class="fs-5 fw-semibold">{{ config('constant.app_name') }}</span>
            </a>
            <button class="btn btn-sm btn-light border d-md-none" id="sidebarToggle1" type="button">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>

        <div class="sidebar-scroll flex-grow-1 overflow-auto">
            <ul class="nav nav-pills flex-column">
                <li><a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-gauge-high me-2"></i> Dashboard</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-user me-2"></i> Area Manager</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-user me-2"></i> Phlebo</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-user-doctor me-2"></i> Doctor</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-warehouse me-2"></i> Collection Centers</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-vial me-2"></i> Manage Test</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-vial-virus me-2"></i> Lab Management</a></li>
                <li><a href="#" class="nav-link"><i class="fa-brands fa-first-order me-2"></i> Manage Orders</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-user me-2"></i> User Management</a></li>
                <li><a href="#" class="nav-link"><i class="fa-regular fa-file-lines me-2"></i> Reports</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-headset me-2"></i> Help & Support</a></li>
                <li><a href="#" class="nav-link"><i class="fa-solid fa-money-bill me-2"></i> Discount & Coupons</a></li>
                
                <li class="nav-link">
                    @php
                        $isSettingsActive = request()->routeIs('settings.*')
                        || request()->routeIs('faqs.*')
                        || request()->routeIs('booking-steps.*');
                    @endphp
                    <li class="nav-item">
                        <a class="nav-link d-flex justify-content-between align-items-center {{ $isSettingsActive ? 'active' : '' }}"
                            data-bs-toggle="collapse"
                            href="#settings"
                            role="button"
                            aria-expanded="{{ $isSettingsActive ? 'true' : 'false' }}"
                            aria-controls="settings">
                            <span><i class="fa-solid fa-gear me-2"></i> Settings</span>
                            <i class="fa-solid fa-caret-down"></i>
                        </a>
                        <div class="collapse ps-3 {{ $isSettingsActive ? 'show' : '' }}" id="settings">
                            <ul class="nav flex-column">

                                <!-- User Settings -->
                                <li>
                                    <a class="nav-link d-flex justify-content-between align-items-center {{ Str::startsWith(Route::currentRouteName(), 'settings.') ? 'active' : '' }}"
                                        data-bs-toggle="collapse"
                                        href="#userSettings"
                                        role="button"
                                        aria-expanded="{{ Str::startsWith(Route::currentRouteName(), 'settings.') ? 'true' : 'false' }}"
                                        aria-controls="userSettings">
                                        <span><i class="fa-solid fa-user me-2"></i> User</span>
                                        <i class="fa-solid fa-caret-down"></i>
                                    </a>
                                    <div class="collapse ps-3 {{ Str::startsWith(Route::currentRouteName(), 'settings.') ? 'show' : '' }}" id="userSettings">
                                        <ul class="nav flex-column">
                                            <li><a class="nav-link {{ request()->routeIs('settings.aboutUsUser') ? 'active' : '' }}" href="{{ route('settings.aboutUsUser') }}">About Us</a></li>
                                            <li><a class="nav-link {{ request()->routeIs('settings.termsConditionsUser') ? 'active' : '' }}" href="{{ route('settings.termsConditionsUser') }}">Terms & Conditions</a></li>
                                            <li><a class="nav-link {{ request()->routeIs('settings.privacyPolicyUser') ? 'active' : '' }}" href="{{ route('settings.privacyPolicyUser') }}">Privacy Policy</a></li>
                                            <li><a class="nav-link {{ request()->routeIs('settings.helpSupportUser') ? 'active' : '' }}" href="{{ route('settings.helpSupportUser') }}">Help & Support</a></li>
                                            <li><a class="nav-link {{ request()->routeIs('settings.rewardPointGuidelinesUser') ? 'active' : '' }}" href="{{ route('settings.rewardPointGuidelinesUser') }}">Reward Point Guidelines</a></li>
                                        </ul>
                                    </div>
                                </li>

                                <!-- Collection Center Settings -->
                                <li>
                                    <a class="nav-link d-flex justify-content-between align-items-center {{ Str::startsWith(Route::currentRouteName(), 'collectionSettings.') ? 'active' : '' }}"
                                        data-bs-toggle="collapse"
                                        href="#collectionSettings"
                                        role="button"
                                        aria-expanded="{{ Str::startsWith(Route::currentRouteName(), 'settings.collection-centers.') ? 'true' : 'false' }}"
                                        aria-controls="collectionSettings">
                                        <span><i class="fa-solid fa-building me-2"></i> Collection Center</span>
                                        <i class="fa-solid fa-caret-down"></i>
                                    </a>
                                    <div class="collapse ps-3 {{ Str::startsWith(Route::currentRouteName(), 'csettings.collection-centers..') ? 'show' : '' }}" id="collectionSettings">
                                        <ul class="nav flex-column">
                                            <li><a class="nav-link " href="#">About Us</a></li>
                                            <li><a class="nav-link" href="#">Terms & Conditions</a></li>
                                            <li><a class="nav-link" href="#">Privacy Policy</a></li>
                                            <li><a class="nav-link" href="#">Help & Support</a></li>
                                        </ul>
                                    </div>
                                </li>

                                <!-- FAQ Settings -->
                                <li><a href="{{ route('faqs.index') }}" class="nav-link {{ request()->routeIs('faqs.index') ? 'active' : '' }}"><i class="fa fa-info-circle" title="FAQ"></i> FAQ</a></li>
                                <li><a href="{{ route('booking-steps.index') }}" class="nav-link {{ request()->routeIs('booking-steps.index') ? 'active' : '' }}"><i class="fa	fa-calendar-alt"></i> Booking Steps</a></li>
                            </ul>
                        </div>
                    </li>
                </li>

                <!-- Masters Dropdown -->
                <li class="nav-item">
                    @php
                        $isMastersActive = request()->is('admin/roles*') ||
                            request()->is('admin/blood-groups*') ||
                            request()->is('admin/states*') || 
                            request()->is('admin/cities*') ||
                            request()->is('admin/associations*') ||
                            request()->is('admin/cancel-reasons*');
                    @endphp
                    <li class="nav-item">
                        <a class="nav-link d-flex justify-content-between align-items-center {{ $isMastersActive ? 'active' : '' }}"
                            data-bs-toggle="collapse"
                            href="#mastersSubmenu"
                            role="button"
                            aria-expanded="{{ $isMastersActive ? 'true' : 'false' }}"
                            aria-controls="mastersSubmenu">
                            <span><i class="fa-solid fa-layer-group me-2"></i> Masters</span>
                            <i class="fa-solid fa-caret-down"></i>
                        </a>

                        <div class="collapse ps-3 {{ $isMastersActive ? 'show' : '' }}" id="mastersSubmenu">
                            <ul class="nav flex-column">
                                {{-- <li><a class="nav-link {{ request()->is('admin/roles*') ? 'active' : '' }}" href="{{ route('roles.index') }}">Roles</a></li> --}}
                                <li><a class="nav-link {{ request()->is('admin/blood-groups*') ? 'active' : '' }}" href="{{ route('blood-groups.index') }}">Blood Group</a></li>
                                <li><a class="nav-link {{ request()->is('admin/states*') ? 'active' : '' }}" href="{{ route('states.index') }}">State</a></li>
                                <li><a class="nav-link {{ request()->is('admin/cities*') ? 'active' : '' }}" href="{{ route('cities.index') }}">City</a></li>
                                <li><a class="nav-link {{ request()->is('admin/associations*') ? 'active' : '' }}" href="{{ route('associations.index') }}">Association</a></li>
                                <li><a class="nav-link {{ request()->is('admin/cancel-reasons*') ? 'active' : '' }}" href="{{ route('cancel-reasons.index') }}">Cancel Reason</a></li>
                            </ul>
                        </div>
                    </li>

                </li>
            </ul>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content bg-light" id="mainContent">
        <main class="container-fluid  pt-2">
            @yield('content')
        </main>

        <footer class="py-3 text-center">
            <p class="text-muted">
                &copy; {{ date('Y') }} <a href="{{ config('constant.developed_by_url') }}">{{ config('constant.developed_by_name') }}</a>. All rights reserved.
            </p>
        </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="{{ asset('assets/admin/bootstrap/bootstrap.min.js') }}"></script>

    {{-- Ck Editor --}}
    <script src="{{ asset('assets/ckeditor/build/ckeditor.js') }}"></script>
    <script>
        ClassicEditor
            .create( document.querySelector( '.editor' ) )
            .catch( error => {
                console.error( error );
        });
    </script>

    @stack('scripts')

    <!-- Sidebar Toggle Script -->
    <script>
        const sidebar = document.getElementById('sidebarMenu');
        const content = document.getElementById('mainContent');
        const toggleBtn = document.getElementById('sidebarToggle');
        const toggleBtn1 = document.getElementById('sidebarToggle1');

        function toggleSidebar() {
            if (window.innerWidth < 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                content.classList.toggle('collapsed');
            }
        }

        toggleBtn.addEventListener('click', toggleSidebar);
        toggleBtn1.addEventListener('click', toggleSidebar);

        // Optional: auto-hide sidebar on link click (mobile)
        document.querySelectorAll('#sidebarMenu .nav-link').forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth < 768) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>