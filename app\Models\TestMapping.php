<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Masters\TestMethod;

class TestMapping extends Model
{
    protected $fillable = [
        'test_id',
        'lab_id',
        'lab_test_code',
        'test_method_id',
        'lab_price',
        'user_price',
        'sld_price',
        'net_price',
        'status',
    ];

    public function test(){
        return $this->belongsTo(Test::class);
    }

    public function lab()
    {
        return $this->belongsTo(Lab::class);
    }

    public function testMethod()
    {
        return $this->belongsTo(TestMethod::class);
    }
}
