<?php

namespace App\Http\Controllers\Masters;

use App\Models\Masters\Category;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $records = Category::paginate(10);
        return view('admin.masters.categories.index', compact('records'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:50|unique:categories,name',
            'icon' => 'nullable|string|max:100',
            'status' => 'required|in:active,inactive',
        ]);

        Category::create($request->only('name', 'icon', 'status'));

        return redirect()->route('categories.index')->with('success', 'Category created successfully.');
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name' => 'required|string|max:50|unique:categories,name,' . $category->id,
            'icon' => 'nullable|string|max:100',
            'status' => 'required|in:active,inactive',
        ]);

        $category->update($request->only('name', 'icon', 'status'));

        return redirect()->route('categories.index')->with('success', 'Category updated successfully.');    
    }
}
