@extends('layouts.admin')

@push('style')
    {{-- <style>
        /* .order-card{
            background: #020024;
            background: linear-gradient(90deg, rgb(13, 10, 71) 0%, rgba(134, 134, 156, 1) 0%, rgba(57, 158, 179, 1) 0%, rgba(57, 154, 173, 1) 100%);
        }
        .ruppe-card {
            background: #833AB4;
            background: linear-gradient(90deg, rgba(131, 58, 180, 1) 0%, rgba(253, 29, 29, 1) 0%, rgba(252, 176, 69, 1) 100%);
        }
        .lab-card {
            background: #020024;
            background: linear-gradient(90deg, rgba(2, 0, 36, 1) 0%, rgba(134, 134, 156, 1) 0%, rgba(73, 179, 57, 1) 0%, rgba(57, 154, 173, 1) 100%);
        }
        .users-card {
            background: #020024;
            background: linear-gradient(90deg, rgba(2, 0, 36, 1) 0%, rgba(134, 134, 156, 1) 0%, rgba(73, 179, 57, 1) 0%, rgba(186, 112, 93, 1) 0%);
        } */

        .stat-card {
        border-radius: 15px;
        transition: transform 0.2s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }

    .bg-order {
        background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .bg-revenue {
        background: linear-gradient(135deg, #f7971e, #ffd200);
    }

    .bg-labs {
        background: linear-gradient(135deg, #43cea2, #185a9d);
    }

    .bg-users {
        background: linear-gradient(135deg, #f953c6, #b91d73);
    }

    .bg-today-order {
        background: linear-gradient(135deg, #30cfd0, #330867);
    }

    .bg-report {
        background: linear-gradient(135deg, #74ebd5, #acb6e5);
    }

    .info-card {
        border-radius: 16px;
        transition: all 0.2s ease-in-out;
    }
    .info-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    }
    .icon-box {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
    }

    body {
        background-color: #f4f6f9;
    }

    .info-card {
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        background-color: #fff;
    }

    .icon-box {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
    }

    .info-card h6 {
        color: #6c757d;
    }

    .info-card h2 {
        color: #212529;
    }

    </style> --}}

    <style>
        
    </style>
@endpush

@push('style')
    <style>
        .icon-box {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            font-size: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hover-card {
            transition: all 0.2s ease-in-out;
            border-radius: 12px;
            background-color: #fff;
        }

        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: #f7f9fb;
        }
    </style>
@endpush


@section('content')
    <x-alert />
    {{-- <header class="mt-4 p-3  rounded shadow-sm border header">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
            <div class="mb-2 mb-md-0">
                <h5 class="mb-1 fw-semibold">
                    <i class="fas fa-project-diagram me-2 text-primary"></i> Projects Details
                </h5>
                <small class="text-muted">Manage and track your project information</small>
            </div>
            <div>
                <a href="#" class="btn btn-primary d-flex align-items-center gap-1">
                    <i class="fas fa-plus"></i> Add New
                </a>
            </div>
        </div>
    </header> --}}

    {{-- <div class="row">
        <div class="col-md-4">
            <div class="card shadow order-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h5>49</h5>
                        <div><i class="fa-solid fa-cart-shopping"></i></div>
                    </div>
                    <progress value="50" max="100" class="col-md-12 mt-2">  </progress>
                    <div class="mt-2">Total Order</div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow ruppe-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h5><i class="fa-solid fa-indian-rupee-sign"></i> 49</h5>
                        <div><i class="fa-solid fa-indian-rupee-sign"></i></div>
                    </div>
                    <progress value="50" max="100" class="col-md-12 mt-2">  </progress>
                    <div class="mt-2">Total Revenue</div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow  lab-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h5>49</h5>
                        <div><i class="fa-solid fa-vial"></i></div>
                    </div>
                    <progress value="50" max="100" class="col-md-12 mt-2">  </progress>
                    <div class="mt-2">Total Labs</div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mt-3">
            <div class="card shadow users-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h5>49</h5>
                        <div><i class="fa-solid fa-users"></i></div>
                    </div>
                    <progress value="50" max="100" class="col-md-12 mt-2">  </progress>
                    <div class="mt-2">Total Users</div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mt-3">
            <div class="card shadow  order-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h5>49</h5>
                        <div><i class="fa-solid fa-cart-shopping"></i></div>
                    </div>
                    <progress value="50" max="100" class="col-md-12 mt-2">  </progress>
                    <div class="mt-2">Today Orders</div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mt-3">
            <div class="card shadow ruppe-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h5>49</h5>
                        <div><i class="fa-solid fa-cart-shopping"></i></div>
                    </div>
                    <progress value="50" max="100" class="col-md-12 mt-2">  </progress>
                    <div class="mt-2">Today Uploaded Reports</div>
                </div>
            </div>
        </div>
    </div> --}}

    <div class="container">
        <div class="row">
            {{-- Total Orders --}}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm hover-card">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Orders</h6>
                            <h2 class="fw-bold mb-2">{{ $totalOrders }}</h2>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-primary" style="width: {{ $orderProgress }}%;"></div>
                            </div>
                        </div>
                        <div class="icon-box bg-primary text-white">
                            <i class="fa-solid fa-cart-shopping"></i>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Total Revenue --}}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm hover-card">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Revenue</h6>
                            <h2 class="fw-bold mb-2">₹{{ $totalRevenue }}</h2>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: {{ $revenueProgress }}%;"></div>
                            </div>
                        </div>
                        <div class="icon-box bg-success text-white">
                            <i class="fa-solid fa-wallet"></i>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Total Labs --}}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm hover-card">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Labs</h6>
                            <h2 class="fw-bold mb-2">{{ $totalLabs }}</h2>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-info" style="width: {{ $labProgress }}%;"></div>
                            </div>
                        </div>
                        <div class="icon-box bg-info text-white">
                            <i class="fa-solid fa-vial"></i>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Total Users --}}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm hover-card">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Total Users</h6>
                            <h2 class="fw-bold mb-2">{{ $totalUsers }}</h2>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: {{ $userProgress }}%;"></div>
                            </div>
                        </div>
                        <div class="icon-box bg-warning text-white">
                            <i class="fa-solid fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Today Orders --}}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm hover-card">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Today Orders</h6>
                            <h2 class="fw-bold mb-2">{{ $todayOrders }}</h2>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-secondary" style="width: {{ $todayOrderProgress }}%;"></div>
                            </div>
                        </div>
                        <div class="icon-box bg-secondary text-white">
                            <i class="fa-solid fa-cart-plus"></i>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Today Uploaded Reports --}}
            <div class="col-md-4 mb-4">
                <div class="card border-0 shadow-sm hover-card">
                    <div class="card-body d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Today Uploaded Reports</h6>
                            <h2 class="fw-bold mb-2">{{ $todayReports }}</h2>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-dark" style="width: {{ $reportProgress }}%;"></div>
                            </div>
                        </div>
                        <div class="icon-box bg-dark text-white">
                            <i class="fa-solid fa-file-medical"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mt-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-flask me-2"></i>Today's Sample Collection Monitoring</h5>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Order No</th>
                            <th>User Details</th>
                            <th>Address</th>
                            <th>Details</th>
                            <th>Amount</th>
                            <th>Accepted By</th>
                            <th>Collected By</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td><span class="badge bg-secondary">ORDER/25/001</span></td>
                            <td>
                                <div class="fw-semibold">Kamlesh Patel</div>
                                <small class="text-muted">831927738</small>
                            </td>
                            <td>Samata Colony, Raipur</td>
                            <td>Blood Sample</td>
                            <td><strong>₹2000</strong></td>
                            <td><span class="badge bg-success">Tushar Sahu</span></td>
                            <td><span class="badge bg-info text-dark">Tushar Sahu</span></td>
                        </tr>
                        <!-- Repeat rows as needed -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>


    <div class="card shadow-sm mt-4">
        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-flask me-2"></i>Sent to Lab Orders</h5>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Order No</th>
                            <th>User</th>
                            <th>Collection Date</th>
                            <th>Amount</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td><span class="badge bg-secondary">ORDER/25/001</span></td>
                            <td>
                                <div class="fw-semibold">Kamlesh Patel</div>
                                <small class="text-muted">(27 / Male)</small>
                            </td>
                            <td>
                                <i class="fa-regular fa-calendar-check me-1 text-primary"></i>
                                24-02-2025<br>
                                <small class="text-muted">(10:00 AM - 11:00 AM)</small>
                            </td>
                            <td><strong>₹2000</strong></td>
                            <td>
                                <a href="{{ route('uploadReport','123asd') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fa-solid fa-upload me-1"></i> Upload Report
                                </a>
                            </td>
                        </tr>

                        {{-- Repeat or use @foreach loop for dynamic data --}}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

@endsection
