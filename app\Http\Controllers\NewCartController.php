<?php

namespace App\Http\Controllers;

use App\Models\NewCart;
use Illuminate\Http\Request;

class NewCartController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(NewCart $newCart)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(NewCart $newCart)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, NewCart $newCart)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(NewCart $newCart)
    {
        //
    }
}
