<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;

class PdfController extends Controller
{
    public function testPdf(){
        $data = [
            'patient' => [
            'name' => '<PERSON><PERSON><PERSON>',
            'code' => 'C000154812',
            'accession' => '0127YA001213',
            'patient_id' => 'KAVIM070189127',
            'gender_age' => '36 Years / Female',
            'drawn_time' => '07/01/2025 12:59:28',
            'reported_time' => '07/01/2025 14:55:10',
        ],
        'results' => [
            'hemoglobin' => 13.0,
            'rbc' => 5.00,
            'wbc' => 7.3,
            'platelet' => 347,
            'hematocrit' => 39.0,
            'mcv' => 78.0,
            'mch' => 26.0,
            'mchc' => 33.3,
            'rdw' => 12.2,
            'mpv' => 9.8,
            'neutrophils' => 59,
            'lymphocytes' => 35,
            'monocytes' => 5,
        ],
        ];

    // $barcode = \DNS1D::getBarcodeHTML($data['patient']['accession'], 'C128', 1.4, 40);
    $barcode = null;
    
    $pdf = Pdf::loadView('pdf.medical_report', compact('data', 'barcode'))->setPaper('a4', 'portrait');
    return $pdf->stream('medical-report.pdf');
    }
}