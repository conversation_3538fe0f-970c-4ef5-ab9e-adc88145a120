<?php

namespace App\Models;

use App\Models\Masters\Organ;
use App\Models\Masters\Category;
use App\Models\Masters\Condition;
use App\Models\Masters\Speciality;
use Illuminate\Database\Eloquent\Model;

class Test extends Model
{
    protected $fillable = [
        'test_name',
        'test_name_also_known_as',
        'organ_id',
        'sld_test_code',
        'container_type_ids',
        'description',
        'specimen_details',
        'pre_test_info',
        'category_id',
        'condition_id',
        'speciality_id',
        'test_usage',
        'components',
        'remark',
        'report_time',
        'stability_room',
        'stability_refrigerator',
        'stability_frozen',
        'fasting_required',
        'is_package',
        'is_popular',
        'is_special',
        'status',
    ];

    public function organ(){
        return $this->belongsTo(Organ::class);
    }

    public function category(){
        return $this->belongsTo(Category::class);
    }

    public function condition(){
        return $this->belongsTo(Condition::class);
    }

    public function speciality(){
        return $this->belongsTo(Speciality::class);
    }
    
}
