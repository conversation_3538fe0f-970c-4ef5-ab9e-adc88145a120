<?php

namespace App\Http\Controllers\Masters;

use App\Models\Masters\Association;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AssociationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $records = Association::latest()
            ->when(request('name'), function ($q) {
                $q->where('name', 'like', '%' . request('name') . '%');
            })
            ->when(request('description'), function ($q) {
                $q->where('description', 'like', '%' . request('description') . '%');
            })
            ->when(request('status'), function ($q) {
                $q->where('status', request('status'));
            })
            ->paginate(10);
        return view('admin.masters.associations.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:associations,name',
            'description' => 'required|string|max:200',
            'status' => 'required|in:active,inactive',
        ]);

        Association::create($validated);

        return redirect()->route('associations.index')->with('success', 'Association created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Association $association)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Association $association)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Association $association)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100|unique:associations,name,' . $association->id,
            'description' => 'required|string|max:200',
            'status' => 'required|in:active,inactive',
        ]);

        $association->update($validated);

        return redirect()->route('associations.index')->with('success', 'Association updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Association $association)
    {
        //
    }
}
