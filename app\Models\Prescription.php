<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Prescription extends Model
{
    protected $fillable = [
        'app_user_id',
        'filename',
        'response',
        'replied_date',
        'role_id',
        'user_id'
    ];

    public function getFilenameUrlAttribute()
    {
        return file_exists(public_path('uploads/app_users_prescriptions/' . $this->filename)) && !empty($this->filename)
            ? asset('uploads/app_users_prescriptions/' . $this->filename) 
            : null;
    }

    public function getCreatedAtAttribute($value){
        return $this->created_at = date('d-m-Y', strtotime($value));
    }
}
