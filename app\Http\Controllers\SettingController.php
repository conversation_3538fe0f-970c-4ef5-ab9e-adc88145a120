<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Setting $setting)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Setting $setting)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Setting $setting)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Setting $setting)
    {
        //
    }

    public function aboutUsUser()
    {
        $aboutUs = Setting::where('key_name', 'user_about_us')->pluck('value')->first();
        return view('admin.settings.user.about-us', compact('aboutUs'));
    }

    public function updateOrCreateAboutUsUser(Request $request)
    {
        $request->validate([
            'value' => 'required|string',
        ]);

        try {
            $aboutUs = Setting::updateOrCreate(['key_name' => 'user_about_us'],
                ['value' => $request->value]);
            $message = $aboutUs->wasRecentlyCreated ? 'About us created successfully' : 'About us updated successfully';
            return to_route('settings.aboutUsUser')->with('success', $message);
        } catch (\Exception $ex) { 
            Log::error('About Us Store Or Update Error!', [
                'ex' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__
            ]);
            return to_route('settings.aboutUsUser')->with('error', 'Something went wrong');
        }
    }

    public function termsConditionsUser()
    {
        $termsConditions = Setting::where('key_name', 'user_terms_conditions')->pluck('value')->first();
        return view('admin.settings.user.terms-conditions', compact('termsConditions'));
    }

    public function updateOrCreateTermsConditionsUser(Request $request)
    {
        $request->validate([
            'value' => 'required|string',
        ]);

        try {
            $termsConditions = Setting::updateOrCreate(['key_name' => 'user_terms_conditions'],
                ['value' => $request->value]);
            $message = $termsConditions->wasRecentlyCreated ? 'Terms and Conditions created successfully' : 'Terms and Conditions updated successfully';
            return to_route('settings.termsConditionsUser')->with('success', $message);
        } catch (\Exception $ex) { 
            Log::error('Terms And Conditions Store Or Update Error!', [
                'ex' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__
            ]);
            return to_route('settings.termsConditionsUser')->with('error', 'Something went wrong');
        }
    }

    public function privacyPolicyUser()
    {
        $privacyPolicy = Setting::where('key_name', 'user_privacy_policy')->pluck('value')->first();
        return view('admin.settings.user.privacy-policy', compact('privacyPolicy'));
    }

    public function updateOrCreatePrivacyPolicyUser(Request $request)
    {
        $request->validate([
            'value' => 'required|string',
        ]);

        try {
            $privacyPolicy = Setting::updateOrCreate(['key_name' => 'user_privacy_policy'],
                ['value' => $request->value]);
            $message = $privacyPolicy->wasRecentlyCreated ? 'User Privacy Policy created successfully' : 'User Privacy Policy updated successfully';
            return to_route('settings.privacyPolicyUser')->with('success', $message);
        } catch (\Exception $ex) { 
            Log::error('User Privacy Policy Store Or Update Error!', [
                'ex' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__
            ]);
            return to_route('settings.privacyPolicyUser')->with('error', 'Something went wrong');
        }
    }

    public function helpSupportUser(){
        $helpSupport = Setting::where('key_name', 'user_help_support')->pluck('value')->first();
        return view('admin.settings.user.help-support', compact('helpSupport'));    
    }

    public function updateOrCreateHelpSupportUser(Request $request){
        $request->validate([
            'value' => 'required|string',
        ]);

        try {
            $helpSupport = Setting::updateOrCreate(['key_name' => 'user_help_support'],
                ['value' => $request->value]);
            $message = $helpSupport->wasRecentlyCreated ? 'Help Support created successfully' : 'Help Support updated successfully';
            return to_route('settings.helpSupportUser')->with('success', $message);
        } catch (\Exception $ex) { 
            Log::error('Help Support Store Or Update Error!', [
                'ex' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__
            ]);
            return to_route('settings.helpSupportUser')->with('error', 'Something went wrong');
        }
    }

    public function rewardPointGuidelinesUser(){
        $rewardPointGuidelines = Setting::where('key_name', 'user_reward_points_guidelines')->pluck('value')->first();
        return view('admin.settings.user.reward-point-guidelines', compact('rewardPointGuidelines'));    
    }

    public function updateOrCreateRewardPointGuidelinesUser(Request $request){
        $request->validate([
            'value' => 'required|string',
        ]);

        try {
            $rewardPointGuidelines = Setting::updateOrCreate(['key_name' => 'user_reward_points_guidelines'],
                ['value' => $request->value]);
            $message = $rewardPointGuidelines->wasRecentlyCreated ? 'Reward Point Guidelines created successfully' : 'Reward Point Guidelines updated successfully';
            return to_route('settings.rewardPointGuidelinesUser')->with('success', $message);
        } catch (\Exception $ex) { 
            Log::error('Reward Point Guidelines Store Or Update Error!', [
                'ex' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__
            ]);
            return to_route('settings.rewardPointGuidelinesUser')->with('error', 'Something went wrong');
        }
    }
}
