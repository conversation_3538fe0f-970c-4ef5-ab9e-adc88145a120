<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Database\Seeders\RoleSeeder; 
use Database\Seeders\UserSeeder;
use Database\Seeders\BloodGroupSeeder;
use Database\Seeders\StateSeeder;
use Database\Seeders\CancelReasonSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        $this->call(RoleSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(BloodGroupSeeder::class);
        $this->call(StateSeeder::class);
        $this->call(CancelReasonSeeder::class);
    }
}
