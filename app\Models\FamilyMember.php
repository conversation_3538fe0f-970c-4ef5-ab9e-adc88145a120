<?php

namespace App\Models;

use App\Models\Masters\Relation;
use App\Models\Masters\BloodGroup;
use Illuminate\Database\Eloquent\Model;

class FamilyMember extends Model
{
    protected $fillable = [
        'app_user_id',
        'name',
        'gender',
        'dob',
        'relation_id',
        'blood_group_id'
    ];

    public function appUser(){
        return $this->belongsTo(AppUser::class);
    }

    public function relation(){
        return $this->belongsTo(Relation::class);
    }

    public function bloodGroup(){
        return $this->belongsTo(BloodGroup::class);
    }
}
