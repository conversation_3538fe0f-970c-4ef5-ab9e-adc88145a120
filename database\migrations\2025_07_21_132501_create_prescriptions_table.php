<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prescriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('app_user_id')->nullable()->constrained('app_users')->onsDelete('set null');
            $table->string('filename',50);
            $table->text('response')->nullable();
            $table->date('replied_date')->nullable();
            $table->foreignId('role_id')->nullable()->constrained('roles')->onsDelete('set null')->comment('replied by');
            $table->foreignId('user_id')->nullable()->constrained('users')->onsDelete('set null')->comment('replied by id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prescriptions');
    }
};
