<?php

namespace App\Http\Controllers;

use App\Models\TestComponent;
use Illuminate\Http\Request;

class TestComponentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(TestComponent $testComponent)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TestComponent $testComponent)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TestComponent $testComponent)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TestComponent $testComponent)
    {
        //
    }
}
