<?php

namespace App\Http\Controllers\Masters;

use App\Models\Masters\State;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class StateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = State::orderBy('name');
        if(isset($request->name)) {
            $name = request()->input('name');
            $records = $records->where('name', 'like', '%' . $name . '%');
        }
        if(isset($request->status)  ) {
            $status = request()->input('status');
            $records = $records->where('status', $status);
        }
        $records = $records->paginate(10);
        return view('admin.masters.states.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:50|unique:states,name',
            'status' => 'required|in:active,inactive',
        ]);

        State::create([
            'name' => $request->name,
            'status' => $request->status,
        ]);

        return redirect()->route('states.index')->with('success', 'State created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(State $state)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(State $state)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, State $state)
    {
        $request->validate([
            'name' => 'required|string|max:50|unique:states,name,' . $state->id,
            'status' => 'required|in:active,inactive',
        ]);

        $state->update([
            'name' => $request->name,
            'status' => $request->status,
        ]);

        return redirect()->route('states.index')->with('success', 'State updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(State $state)
    {
        //
    }
}
