<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use DB;

class BloodGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (DB::table('blood_groups')->count() > 0) {
            return; // Skip seeding if blood groups already exist
        }
        
        \DB::table('blood_groups')->insert([
            ['name' => 'A+', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'A-', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'B+', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'B-', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'AB+', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'AB-', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'O+', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'O-', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Bombay Blood Group', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
        ]);
    }
}
