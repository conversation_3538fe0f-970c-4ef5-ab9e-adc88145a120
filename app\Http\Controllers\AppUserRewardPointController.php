<?php

namespace App\Http\Controllers;

use App\Models\AppUserRewardPoint;
use Illuminate\Http\Request;

class AppUserRewardPointController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(AppUserRewardPoint $appUserRewardPoint)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AppUserRewardPoint $appUserRewardPoint)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AppUserRewardPoint $appUserRewardPoint)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AppUserRewardPoint $appUserRewardPoint)
    {
        //
    }
}
