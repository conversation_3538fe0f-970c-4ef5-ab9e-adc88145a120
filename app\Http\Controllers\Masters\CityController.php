<?php

namespace App\Http\Controllers\Masters;

use App\Models\Masters\State;
use App\Models\Masters\City;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CityController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $records = City::with('state')
            ->when(request('name'), function ($query) {
                $query->where('name', 'like', '%' . request('name') . '%');
            })
            ->when(request('state_id'), function ($query) {
                $query->where('state_id', request('state_id'));
            })
            ->when(request('status'), function ($query) {
                $query->where('status', request('status'));
            })
            ->orderBy('state_id')
            ->orderBy('name')
            ->paginate(10);
        $states = State::select('id', 'name')->where('status','active')->get();
        $allStates = State::select('id', 'name')->orderBy('name')->get();
        return view('admin.masters.cities.index', compact('records','states','allStates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:states,id',
            'public_status' => 'nullable|numeric|digits:1',
            'status' => 'required|in:active,inactive',
        ]);

        // Check for unique city name in the same state
        $existingCity = City::where('name', $request->name)
            ->where('state_id', $request->state_id)
            ->first();
        if ($existingCity) {
            return redirect()->back()->withErrors(['name' => 'The city name must be unique within the same state.']);
        }

        City::create($request->all());

        return redirect()->route('cities.index')->with('success', 'City created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(City $city)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(City $city)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, City $city)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:states,id',
            'public_status' => 'nullable|numeric|digits:1',
            'status' => 'required|in:active,inactive',
        ]);
        
        // Check for unique city name in the same state
        $existingCity = City::where('name', $request->name)
            ->where('state_id', $request->state_id)
            ->where('id', '!=', $city->id) // Exclude the current city being updated
            ->first();
        if ($existingCity) {
            return redirect()->back()->withErrors(['name' => 'The city name must be unique within the same state.']);
        }   

        $city->update($request->all());

        return redirect()->route('cities.index')->with('success', 'City updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(City $city)
    {
        //
    }
}
