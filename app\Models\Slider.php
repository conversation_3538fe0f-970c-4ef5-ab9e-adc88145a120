<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Slider extends Model
{
    protected $fillable = [
        'title',
        'image',
        'sequence',
        'type',
        'status',
    ];

    public function getImgAttribute(){
        return file_exists(public_path('uploads/sliders/' . $this->image)) && !empty($this->image)
            ? asset('uploads/sliders/' . $this->image) 
            : null;
    }
}
