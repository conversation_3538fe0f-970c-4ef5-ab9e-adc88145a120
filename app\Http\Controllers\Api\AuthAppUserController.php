<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AppUser;
use App\Models\AppUserRewardPoint;
use App\Models\Masters\BloodGroup;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;

class AuthAppUserController extends Controller
{
    /**
         * @OA\Post(
         *     path="/api/register-login-otp",
         *     tags={"Authentication"},
         *     summary="Register or login user using OTP",
         *     description="Validates mobile number and sends a 6-digit OTP for login or registration. If user does not exist, creates a new user.",
         *     operationId="registerLoginOtp",
         *     
         *     @OA\RequestBody(
         *         required=true,
         *         @OA\JsonContent(
         *             required={"mobile"},
         *             @OA\Property(property="mobile", type="string", pattern="^\d{10}$", example="9876543210", description="User's 10-digit mobile number")
         *         )
         *     ),

        *     @OA\Response(
        *         response=200,
        *         description="OTP sent successfully",
        *         @OA\JsonContent(
        *             @OA\Property(property="status", type="boolean", example=true),
        *             @OA\Property(property="message", type="string", example="OTP sent successfully"),
        *             @OA\Property(
        *                 property="data",
        *                 type="object",
        *                 @OA\Property(property="mobile", type="string", example="9876543210"),
        *                 @OA\Property(property="otp", type="string", example="123456")
        *             )
        *         )
        *     ),

        *     @OA\Response(
        *         response=422,
        *         description="Validation Error",
        *         @OA\JsonContent(
        *             @OA\Property(property="status", type="boolean", example=false),
        *             @OA\Property(property="message", type="string", example="Validation Error!"),
        *             @OA\Property(property="error", type="string", example="The mobile field is required.")
        *         )
        *     )
        * )
    */

    public function registerLoginOtp(Request $request){
        $validated = Validator::make($request->all(),[
            'mobile' => 'required|numeric|digits:10',
        ]);
        if ($validated->fails()) {
            return response()->json(['status' => false, 'message'=>'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        $mobile = $request->mobile;

        // Generate a 6-digit OTP
        // $otp = rand(100000, 999999);
        $otp = '123456';

        // Check if the user already exists
        $appUser = AppUser::where('mobile', $mobile)->first();

        if (!$appUser) {
            // Create a new user if not exists
            $appUser = AppUser::create([
                'mobile' => $mobile,
                'otp' => $otp,
                'password' => Hash::make($otp), // Store OTP as password for login
            ]);
        } else {
            // Update OTP for existing user
            $appUser->update(['otp' => $otp,
                'password' => Hash::make($otp), 
            ]);
            
        }

        // Here you would typically send the OTP to the user's mobile via SMS

        $data = [
            'mobile' => $appUser->mobile,
            'otp' => $appUser->otp,
        ];

        return response()->json(['status' => true, 'message' => 'OTP sent successfully', 'data' => $data], 200);
    }

    public function otpVerify(Request $request){
        $validated = Validator::make($request->all(), [
            'mobile' => 'required|numeric|digits:10|exists:app_users,mobile',
            'otp' => 'required|numeric|digits:6',
        ]);
        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        $mobile = $request->mobile;
        $otp = $request->otp;
        $appUser = AppUser::where('mobile', $mobile)->where('otp', $otp)->first();

        if (!$appUser) {
            return response()->json(['status' => false, 'message'=>'Invalid OTP', 'error' => 'Invalid OTP'], 422);
        }

        $data = $appUser;
        $data->token =  $appUser->createToken('app-user-token')->plainTextToken;
        return response()->json(['status' => true, 'message' => 'OTP verified successfully', 'data' => ['user' => $appUser]], 200);
    }

    public function profile(Request $request){
        $appUser = $request->user();
        $appUser['dob'] = formatDateToDMY($appUser->dob);
        $appUser['profile_image_url'] =  $appUser->profile_image_url;
        $appUser['blood_group'] = BloodGroup::where('id',$appUser->blood_group_id)->pluck('name')->first();
        $appUser['total_points'] = appUserCurrentReward($request->user()->id);
        $appUser['state'] = $appUser->state->name;
        $appUser['city'] = $appUser->city->name;
        return response()->json(['status' => true, 'message' => 'Profile fetched successfully', 'data' => $appUser], 200);
    }

    public function updateRegisterProfile1(Request $request){
        $appUser = $request->user();

        $validated = Validator::make($request->all(), [
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);
        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        $appUser->update([
            'state_id' => $request->state_id,
            'city_id' => $request->city_id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
        ]);

        // Add 300 points to user's account
        AppUserRewardPoint::create([
            'app_user_id' => $appUser->id,
            'points' => 300,
            'type' => 'CR',
            'expiry_date' => date('Y-m-d', strtotime('+1 year')),
            'remark' => 'WELCOME',
        ]);

        return response()->json(['status' => true, 'message' => 'Regiaster Profile updated successfully', 'data' => $appUser], 200);
    }

    public function updateProfile(Request $request){
        $validated = Validator::make($request->all(),[
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:50',
            'profile_image' => 'nullable|mimes:jpg,jpeg,png,webp|max:2048',
            'gender' => 'nullable|string',
            'blood_group_id' => 'nullable|exists:blood_groups,id',
            'dob' => 'nullable|date',
            'state_id' => 'nullable|exists:states,id',
            'city_id' => 'nullable|exists:cities,id',
            'address' => 'nullable|max:255,string',
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        $appUser = AppUser::find($request->user()->id);
        $oldImageProfile = $appUser->profile_image;
        if(isset($request->profile_image)){
            $profileImage = date('dmYHis') . rand(111111,999999)  . '.' . $request->profile_image->getClientOriginalExtension();
            $request->file('profile_image')->move(public_path('uploads/app_users/'),$profileImage);

            if(file_exists(public_path('uploads/app_users/'.$oldImageProfile)) && !empty($oldImageProfile)){
                unlink(public_path('uploads/app_users/'.$oldImageProfile));
            }
        }

        $appUser->update([
            'name' => $request->name,
            'email' => $request->email,
            'profile_image' => $profileImage ?? $oldImageProfile,
            'gender' => $request->gender,
            'blood_group_id' => $request->blood_group_id,
            'dob' => date('Y-m-d', strtotime($request->dob)),
            'state_id' => $request->state_id,
            'city_id' => $request->city_id,
            'address' => $request->address,
        ]);

        return response()->json(['status' => true, 'message' => 'App User Profile updated successfully'], 200);
    }

    public function logout(Request $request){
        $request->user()->currentAccessToken()->delete();
        return response()->json(['status' => true, 'message' => 'Logged out successfully'], 200);
    }
}
