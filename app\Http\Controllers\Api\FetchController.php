<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Masters\Category;
use App\Models\Masters\State;
use App\Models\Masters\City;
use App\Models\Masters\Organ;
use App\Models\Masters\Container;
use App\Models\Masters\Condition;
use App\Models\Masters\Speciality;
use App\Models\Masters\Parameter;
use App\Models\Masters\Relation;
use App\Models\Masters\Service;
use App\Models\Masters\BloodGroup;
use App\Models\Masters\TestMethod;
use App\Models\Lab;
use App\Models\Test;
use App\Models\CollectionCenter;
use App\Models\AppUser;
use App\Models\Masters\Component;
use App\Models\TestComponent;
use App\Models\NewCart;
use Illuminate\Http\Request;

class FetchController extends Controller
{
    /**
         * @OA\Get(
         *     path="/api/categories",
         *     tags={"Categories"},
         *     summary="Fetch Categories",
         *     description="Retrieve a list of categories with optional filtering by ID.",
         *     @OA\Parameter(
         *         name="id",
         *         in="query",
         *         description="Filter by category ID",
         *         required=false,
         *         @OA\Schema(type="integer")
         *     ),
         *     @OA\Response(
         *         response=200,
         *         description="Successful operation",
         *         @OA\JsonContent(
         *             type="object",
         *             @OA\Property(property="status", type="boolean", example=true),
         *             @OA\Property(property="message", type="string", example="Category Data"),
         *             @OA\Property(
         *                 property="data",
         *                 type="array",
         *                 @OA\Items(
         *                     type="object",
         *                     @OA\Property(property="id", type="integer", example=1),
         *                     @OA\Property(property="name", type="string", example="Electronics"),
         *                     @OA\Property(property="icon", type="string", example="fa fa-tv")
         *                 )
         *             )
         *         )
         *     )
         * )
    */
    public function categories(Request $request)
    {
        $categories = Category::select('id', 'name', 'icon')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();

        return response()->json(['status'=>true, 'message'=>'Category Data', 'data'=>$categories], 200);
    }

    /**
     * @OA\Get(
     *     path="/api/states",
     *     tags={"State Location"},
     *     summary="Fetch active states",
     *     description="Returns a list of active states (id and name only).",
     *     operationId="getStates",
     *     @OA\Response(
     *         response=200,
     *         description="Successful response",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="States fetched successfully!"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Gujarat")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function states(Request $request)
    {
        $states = State::select('id','name')->where('status','active')->get();
        return response()->json(['status'=>true, 'message'=>'States fetched successfully!', 'data'=>$states], 200);
    }

    /**
     * @OA\Get(
     *     path="/api/cities",
     *     tags={"City Location"},
     *     summary="Fetch active cities",
     *     description="Returns a list of active cities. Optionally filter by state_id.",
     *     operationId="getCities",
     *     @OA\Parameter(
     *         name="state_id",
     *         in="query",
     *         description="Filter cities by state ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful response",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Cities fetched successfully!"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=101),
     *                     @OA\Property(property="name", type="string", example="Ahmedabad"),
     *                     @OA\Property(property="state_id", type="integer", example=1),
     *                     @OA\Property(
     *                         property="state",
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Gujarat")
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */

    public function cities(Request $request){
        $cities = City::with('state:id,name')
            ->select('id','name','state_id')
            ->where('status','active')
            ->when($request->has('state_id'), function ($query) use ($request) {
                $query->where('state_id',$request->state_id);
            })
            ->get();
        return response()->json(['status'=>true, 'message'=>'Cities fetched successfully!', 'data'=>$cities], 200);
    }

    /**
     * @OA\Get(
     *     path="/api/organs",
     *     tags={"Oragans"},
     *     summary="Fetch active organs",
     *     description="Returns a list of active organs. You can optionally filter by organ ID.",
     *     operationId="getOrgans",
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="Filter by organ ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful response",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Organ fetched successfully!"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Heart"),
     *                     @OA\Property(property="icon", type="string", example="heart.png")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function organs(Request $request)
    {
        $organs = Organ::select('id', 'name', 'icon')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();

        return response()->json(['status'=>true, 'message'=>'Organ fetched successfullt!', 'data'=>$organs], 200);
    }

    /**
         * @OA\Get(
         *     path="/api/containers",
         *     tags={"Containers"},
         *     summary="Fetch active containers",
         *     description="Returns a list of active containers. Optionally filter by container ID.",
         *     operationId="getContainers",
         *     @OA\Parameter(
         *         name="id",
         *         in="query",
         *         description="Filter by container ID",
         *         required=false,
         *         @OA\Schema(type="integer", example=1)
         *     ),
         *     @OA\Response(
         *         response=200,
         *         description="Successful response",
         *         @OA\JsonContent(
         *             @OA\Property(property="status", type="boolean", example=true),
         *             @OA\Property(property="message", type="string", example="Container fetched successfully!"),
         *             @OA\Property(
         *                 property="data",
         *                 type="array",
         *                 @OA\Items(
         *                     @OA\Property(property="id", type="integer", example=1),
         *                     @OA\Property(property="name", type="string", example="Serum Separator Tube"),
         *                     @OA\Property(property="tube_color", type="string", example="Yellow")
         *                 )
         *             )
         *         )
         *     )
         * )
    */
    public function containers(Request $request)
    {
        $containers = Container::select('id', 'name', 'tube_color')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();

        return response()->json(['status'=>true, 'message'=>'Container fetched successfullt!', 'data'=>$containers], 200);
    }

    /**
         * @OA\Get(
         *     path="/api/conditions",
         *     tags={"Conditions"},
         *     summary="Fetch conditions",
         *     description="Returns a list of conditions. You can optionally filter by ID and status.",
         *     operationId="getConditions",

        *     @OA\Parameter(
        *         name="id",
        *         in="query",
        *         description="Filter by Condition ID",
        *         required=false,
        *         @OA\Schema(type="integer", example=1)
        *     ),
        *     @OA\Response(
        *         response=200,
        *         description="Successful response",
        *         @OA\JsonContent(
        *             @OA\Property(property="status", type="boolean", example=true),
        *             @OA\Property(property="message", type="string", example="Condition fetched successfully!"),
        *             @OA\Property(
        *                 property="data",
        *                 type="array",
        *                 @OA\Items(
        *                     @OA\Property(property="id", type="integer", example=1),
        *                     @OA\Property(property="name", type="string", example="Diabetes")
        *                 )
        *             )
        *         )
        *     )
        * )
    */

    public function conditions(Request $request)
    {
        $conditions = Condition::select('id', 'name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();

        return response()->json(['status'=>true, 'message'=>'Condition fetched successfullt!', 'data'=>$conditions], 200);
    }

    /**
         * @OA\Get(
         *     path="/api/specialities",
         *     tags={"Specialities"},
         *     summary="Fetch specialities",
         *     description="Returns a list of specialities. You can optionally filter by ID and status.",
         *     operationId="getSpecialities",

        *     @OA\Parameter(
        *         name="id",
        *         in="query",
        *         description="Filter by Speciality ID",
        *         required=false,
        *         @OA\Schema(type="integer", example=1)
        *     ),

        *     @OA\Response(
        *         response=200,
        *         description="Successful response",
        *         @OA\JsonContent(
        *             @OA\Property(property="status", type="boolean", example=true),
        *             @OA\Property(property="message", type="string", example="Speciality fetched successfully!"),
        *             @OA\Property(
        *                 property="data",
        *                 type="array",
        *                 @OA\Items(
        *                     @OA\Property(property="id", type="integer", example=1),
        *                     @OA\Property(property="name", type="string", example="Cardiology")
        *                 )
        *             )
        *         )
        *     )
        * )
    */

    public function specialities(Request $request)
    {
        $specialities = Speciality::select('id', 'name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();

        return response()->json(['status'=>true, 'message'=>'Speciality fetched successfullt!', 'data'=>$specialities], 200);
    }

    public function labs(Request $request){
        $labs = Lab::with('state:id,name', 'city:id,name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get()
            ->map(function($row){
                $row->logo_url = $row->logo_url;
                return $row;
            });

        return response()->json(['status'=>true, 'message'=>'Lab Data', 'data'=>$labs], 200);
    }
    /**
         * @OA\Get(
         *     path="/api/tests",
         *     tags={"Tests"},
         *     summary="Fetch tests",
         *     description="Get a list of diagnostic tests with filters and relationships.",
         *     operationId="getTests",

        *     @OA\Parameter(
        *         name="id",
        *         in="query",
        *         description="Filter by Test ID",
        *         required=false,
        *         @OA\Schema(type="integer", example=1)
        *     ),
        *     @OA\Parameter(
        *         name="fasting_required",
        *         in="query",
        *         description="Filter by fasting requirement (active/inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="active")
        *     ),
        *     @OA\Parameter(
        *         name="is_package",
        *         in="query",
        *         description="Filter by package tests (active/inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="inactive")
        *     ),
        *     @OA\Parameter(
        *         name="is_popular",
        *         in="query",
        *         description="Filter by popular tests (active/inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="active")
        *     ),
        *     @OA\Parameter(
        *         name="is_special",
        *         in="query",
        *         description="Filter by special tests (active/inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="inactive")
        *     ),
        *     @OA\Parameter(
        *         name="status",
        *         in="query",
        *         description="Filter by status (active or inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="active")
        *     ),

        *     @OA\Response(
        *         response=200,
        *         description="Successful response",
        *         @OA\JsonContent(
        *             @OA\Property(property="status", type="boolean", example=true),
        *             @OA\Property(property="message", type="string", example="Test fetched successfully!"),
        *             @OA\Property(
        *                 property="data",
        *                 type="array",
        *                 @OA\Items(
        *                     @OA\Property(property="id", type="integer", example=1),
        *                     @OA\Property(property="name", type="string", example="Lipid Profile"),
        *                     @OA\Property(property="fasting_required", type="string", enum={"active", "inactive"}, example="active"),
        *                     @OA\Property(property="is_package", type="string", enum={"active", "inactive"}, example="inactive"),
        *                     @OA\Property(property="is_popular", type="string", enum={"active", "inactive"}, example="active"),
        *                     @OA\Property(property="is_special", type="string", enum={"active", "inactive"}, example="inactive"),

        *                     @OA\Property(
        *                         property="organ",
        *                         type="object",
        *                         @OA\Property(property="id", type="integer", example=2),
        *                         @OA\Property(property="name", type="string", example="Heart")
        *                     ),
        *                     @OA\Property(
        *                         property="category",
        *                         type="object",
        *                         @OA\Property(property="id", type="integer", example=3),
        *                         @OA\Property(property="name", type="string", example="Blood Test")
        *                     ),
        *                     @OA\Property(
        *                         property="condition",
        *                         type="object",
        *                         @OA\Property(property="id", type="integer", example=5),
        *                         @OA\Property(property="name", type="string", example="Diabetes")
        *                     ),
        *                     @OA\Property(
        *                         property="speciality",
        *                         type="object",
        *                         @OA\Property(property="id", type="integer", example=4),
        *                         @OA\Property(property="name", type="string", example="Cardiology")
        *                     )
        *                 )
        *             )
        *         )
        *     )
        * )
    */

    public function tests(Request $request)
    { 
        $tests = Test::with('organ:id,name','category:id,name','condition:id,name','speciality:id,name')
            ->when(isset($request->id), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->when(isset($request->fasting_required), function ($query) use ($request) {
                $query->where('fasting_required', $request->fasting_required);
            })
            ->when(isset($request->is_package), function ($query) use ($request) {
                $query->where('is_package', $request->is_package);
            })
            ->when(isset($request->is_popular), function ($query) use ($request) {
                $query->where('is_popular', $request->is_popular);
            })
            ->when(isset($request->is_special), function ($query) use ($request) {
                $query->where('is_special', $request->is_special);
            })
            ->when(isset($request->category_id), function ($query) use ($request) {
                $query->where('category_id', $request->category_id);
            })
            ->where('status','active')
            ->get()->map(function($row)  use ($request) {
                $parameterIds = TestComponent::where('test_id',$row->id)->pluck('parameter_id')->filter();
                $row->covered_parameters = count($parameterIds);
                $row->parameters = Parameter::select('id','title')->whereIn('id',$parameterIds)->get();
                return $row;
            });

        return response()->json(['status'=>true, 'message'=>'Test fetched successfullt!', 'data'=>$tests], 200);
    }

    /**
         * @OA\Get(
         *     path="/api/collection-centers",
         *     operationId="getCollectionCenters",
         *     tags={"Collection Centers"},
         *     summary="Get all active collection centers",
         *     description="Returns a list of active collection centers. You can filter by ID.",
         *     @OA\Parameter(
         *         name="id",
         *         in="query",
         *         description="Filter by Collection Center ID",
         *         required=false,
         *         @OA\Schema(type="integer")
         *     ),
         *     @OA\Response(
         *         response=200,
         *         description="Successful response",
         *         @OA\JsonContent(
         *             @OA\Property(property="status", type="boolean", example=true),
         *             @OA\Property(property="message", type="string", example="Collection Center fetched successfully!"),
         *             @OA\Property(
         *                 property="data",
         *                 type="array",
         *                 @OA\Items(
         *                     @OA\Property(property="id", type="integer", example=1),
         *                     @OA\Property(property="name", type="string", example="XYZ Lab"),
         *                     @OA\Property(property="status", type="string", example="active"),
         *                     @OA\Property(
         *                         property="user",
         *                         type="object",
         *                         @OA\Property(property="id", type="integer", example=5),
         *                         @OA\Property(property="name", type="string", example="John Doe")
         *                     ),
         *                     @OA\Property(
         *                         property="state",
         *                         type="object",
         *                         @OA\Property(property="id", type="integer", example=10),
         *                         @OA\Property(property="name", type="string", example="Maharashtra")
         *                     ),
         *                     @OA\Property(
         *                         property="city",
         *                         type="object",
         *                         @OA\Property(property="id", type="integer", example=22),
         *                         @OA\Property(property="name", type="string", example="Pune")
         *                     ),
         *                     @OA\Property(
         *                         property="timings",
         *                         type="array",
         *                         @OA\Items(
         *                             type="object",
         *                             @OA\Property(property="key", type="integer", example=1),
         *                             @OA\Property(property="day", type="string", example="Monday"),
         *                             @OA\Property(property="timing", type="string", example="10:00:00 - 18:00:00")
         *                         )
         *                     )
         *                 )
         *             )
         *         )
         *     ),
         *     @OA\Response(
         *         response=500,
         *         description="Internal Server Error"
         *     )
         * )
    */

    public function collectionCenters(Request $request){
        $collectionCenters = CollectionCenter::with('user:id,name','state:id,name','city:id,name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get()->map(function($row){
                $row->rating = 5;
                $row->timings = [
                    [
                        'key' => 1,
                        'day' => 'Monday',
                        'timing' => "$row->opening_time_monday - $row->closing_time_monday"
                    ],
                    [
                        'key' => 2,
                        'day' => 'Tuesday',
                        'timing' => "$row->opening_time_tuesday - $row->closing_time_tuesday"
                    ],
                    [
                        'key' => 3,
                        'day' => 'Wednesday',
                        'timing' => "$row->opening_time_wednesday - $row->closing_time_wednesday"
                    ],
                    [
                        'key' => 4,
                        'day' => 'Thursday',
                        'timing' => "$row->opening_time_thursday - $row->closing_time_thursday"
                    ],
                    [
                        'key' => 5,
                        'day' => 'Friday',
                        'timing' => "$row->opening_time_friday - $row->closing_time_friday"
                    ],
                    [
                        'key' => 6,
                        'day' => 'Saturday',
                        'timing' => "$row->opening_time_saturday - $row->closing_time_saturday"
                    ],
                    [
                        'key' => 7,
                        'day' => 'Sunday',
                        'timing' => "$row->opening_time_sunday - $row->closing_time_sunday"
                    ],
                ];
                return $row;
            });
        return response()->json(['status'=>true, 'message'=>'Collection Center fetched successfullt!', 'data'=>$collectionCenters], 200);
    }

    public function components(Request $request){
        $components = Component::select('id','name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();
        return response()->json(['status'=>true, 'message'=>'Component fetched successfully!', 'data'=>$components], 200);
    }

    public function parameters(Request $request){
        $parameters = Parameter::select('id','title','component_ids')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();
        return response()->json(['status'=>true, 'message'=>'Component fetched successfully!', 'data'=>$parameters], 200);
    }

    public function relations(Request $request){
        $relations = Relation::select('id','name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();
        return response()->json(['status'=>true, 'message'=>'Relation fetched successfully!', 'data'=>$relations], 200);
    }

    public function services(Request $request){
        $services = Service::select('id','name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();
        return response()->json(['status'=>true, 'message'=>'Service fetched successfully!', 'data'=>$services], 200);
    }

    public function bloodGroups(Request $request){
        $bloodGroups = BloodGroup::select('id','name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();
        return response()->json(['status'=>true, 'message'=>'Blood Group fetched successfully!', 'data'=>$bloodGroups], 200);
    }

    public function testMethods(Request $request){
        $testMethods = TestMethod::select('id','name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->where('status','active')->get();
        return response()->json(['status'=>true, 'message'=>'Test Method fetched successfully!', 'data'=>$testMethods], 200);
    }

    public function appDetails(){
        $data = [
            'app_name' => config('constant.app_name'),
            'app_mobile' => config('constant.app_mobile'),
            'app_email' => config('constant.app_email'),
            'admin_address' => config('constant.admin_address'),
            'developed_by_url' => config('constant.developed_by_name'),
            'logo' => file_exists(public_path('logo-img.png')) ? asset('logo-img.png') : 'null',
        ];
        return response()->json(['status'=>true, 'message'=>'App Details Fetched Successfully!', 'data'=>$data], 200); 
    }
}
