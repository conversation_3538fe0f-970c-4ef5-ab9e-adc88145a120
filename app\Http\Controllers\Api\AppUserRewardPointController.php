<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AppUserRewardPoint;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class AppUserRewardPointController extends Controller
{
        public function index(Request $request){
        try {
            $rewardHistory = AppUserRewardPoint::where('app_user_id',$request->user()->id)
                ->get()
                ->map(function ($row) {
                    return [
                        'id' => $row->id,
                        'app_user_id' => $row->app_user_id,
                        'order_id' => $row->order_id,
                        'points' => ($row->type == 'CR' ? '+' : ($row->type == 'DR' ? '-' : '')) . $row->points,
                        'type' => $row->type,
                        'expiry_date' => $row->expiry_date,
                        'reason' => $row->reason ? $row->reason . ' POINTS' : null,
                        'remark' => $row->remark ? $row->remark . ' POINTS' : null,
                        'created_at' => formatDateToDMY($row->created_at),
                    ];
                })->toArray();
                
            $data = [
                'reward_hstory' => $rewardHistory,
                'total_points' => appUserCurrentReward($request->user()->id),
                'reward_points_guidelines' => Setting::where('key_name', 'user_reward_points_guidelines')->first()->value ?? null,
            ];
            return response()->json(['status'=>true, 'message'=>'User Reward History Fetched Successfully!', 'data'=>$data], 200);
        } catch (\Exception $ex) {
            Log::error("App User Reward History Fetch Error: ", [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }
}
