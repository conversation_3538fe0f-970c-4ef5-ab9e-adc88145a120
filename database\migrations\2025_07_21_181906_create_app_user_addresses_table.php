<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_user_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('app_user_id')->constrained('app_users')->onsDelete('cascade');
            $table->string('name',100);
            $table->string('mobile',10);
            $table->string('house_no',50);
            $table->string('area',100);
            $table->string('landmark',100);
            $table->string('pin_code',6);
            $table->string('geo_location_latitude',50);
            $table->string('geo_location_longitude',50);
            $table->foreignId('state_id')->nullable()->constrained('states')->onsDelete('set null');
            $table->foreignId('city_id')->nullable()->constrained('cities')->onsDelete('set null');
            $table->enum('type',['Home','Work','Other']);
            $table->enum('use_for_billing',['1','0'])->default('0')->comment('use this address for billing 1:yes 0:no');
            $table->enum('status',['active','inactive'])->default('active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_user_addresses');
    }
};
