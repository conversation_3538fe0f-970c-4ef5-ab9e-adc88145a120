<?php

namespace App\Http\Controllers;

use App\Models\Phlebo;
use Illuminate\Http\Request;

class PhleboController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Phlebo $phlebo)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Phlebo $phlebo)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Phlebo $phlebo)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Phlebo $phlebo)
    {
        //
    }
}
