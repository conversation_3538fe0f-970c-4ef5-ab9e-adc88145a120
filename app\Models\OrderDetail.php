<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderDetail extends Model
{
    protected $fillable = [
        'app_user_id',
        'order_id',
        'test_mapping_id',
        'order_for',
        'family_member_id',
        'test_amount',
        'net_amount',
        'order_status',
        'collection_date_time',
        'sent_to_lab_date_time',
        'report_date_time',
        'test_report'
    ];

    public function appUser(){
        return $this->belongsTo(AppUser::class);
    }

    public function order(){
        return $this->belongsTo(Order::class);
    }

    public function testMapping(){
        return $this->belongsTo(TestMapping::class);
    }

    public function familyMember(){
        return $this->belongsTo(FamilyMember::class);
    }
}
