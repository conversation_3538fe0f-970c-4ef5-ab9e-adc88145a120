<?php

namespace App\Http\Controllers;

use App\Models\TestMapping;
use Illuminate\Http\Request;

class TestMappingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(TestMapping $testMapping)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TestMapping $testMapping)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TestMapping $testMapping)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TestMapping $testMapping)
    {
        //
    }
}
