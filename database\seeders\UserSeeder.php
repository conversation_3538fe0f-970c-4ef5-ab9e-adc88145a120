<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (User::where('email', '<EMAIL>')->where('deleted_at',null)->exists()) {
            return; // Skip if the admin user already exists
        }
        User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'), 
            'role_id' => 1, 
            'status' => 'active',
        ]);
    }
}
