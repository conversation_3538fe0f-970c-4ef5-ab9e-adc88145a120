<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use App\Models\Masters\State;
use App\Models\Masters\City;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\CollectionCenter as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;

class CollectionCenter extends Model
{
    use HasFactory, Notifiable, HasApiTokens;

    protected $fillable = [
        'user_id',
        'center_code',
        'title',
        'center_name',
        'owner_name',
        'dob',
        'gender',
        'blood_group_id',
        'mobile_no',
        'alternate_mobile_no',
        'email',
        'password',
        'profile',
        'state_id',
        'city_id',
        'address',
        'pincode',
        'latitude',
        'longitude',
        'opening_time_monday',
        'closing_time_monday',
        'opening_time_tuesday',
        'closing_time_tuesday',
        'closing_time_wednesday',
        'opening_time_thursday',
        'closing_time_thursday',
        'opening_time_friday',
        'closing_time_friday',
        'opening_time_saturday',
        'closing_time_saturday',
        'opening_time_sunday',
        'closing_time_sunday',
        'status',
    ];

    protected $hidden = [
        'password',
    ];

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function state(){
        return $this->belongsTo(State::class);
    }

    public function city(){
        return $this->belongsTo(City::class);
    }

    protected function formatTime($value)
    {
        return $value ? date('h:i A', strtotime($value)) : null;
    }

   // Accessors
    public function getOpeningTimeMondayAttribute($value) { return $this->formatTime($value); }
    public function getClosingTimeMondayAttribute($value) { return $this->formatTime($value); }

    public function getOpeningTimeTuesdayAttribute($value) { return $this->formatTime($value); }
    public function getClosingTimeTuesdayAttribute($value) { return $this->formatTime($value); }

    public function getOpeningTimeWednesdayAttribute($value) { return $this->formatTime($value); }
    public function getClosingTimeWednesdayAttribute($value) { return $this->formatTime($value); }

    public function getOpeningTimeThursdayAttribute($value) { return $this->formatTime($value); }
    public function getClosingTimeThursdayAttribute($value) { return $this->formatTime($value); }

    public function getOpeningTimeFridayAttribute($value) { return $this->formatTime($value); }
    public function getClosingTimeFridayAttribute($value) { return $this->formatTime($value); }

    public function getOpeningTimeSaturdayAttribute($value) { return $this->formatTime($value); }
    public function getClosingTimeSaturdayAttribute($value) { return $this->formatTime($value); }

    public function getOpeningTimeSundayAttribute($value) { return $this->formatTime($value); }
    public function getClosingTimeSundayAttribute($value) { return $this->formatTime($value); }

    public function getProfileImageUrlAttribute(){
        return file_exists(public_path('uploads/collection-centers/' . $this->profile)) && !empty($this->profile)
            ? asset('uploads/collection-centers/' . $this->profile) 
            : null;
    }
}