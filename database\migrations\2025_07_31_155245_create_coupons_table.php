<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->string('code',10);
            $table->string('title',100);
            $table->string('coupon_for',20); //User or Center
            $table->enum('discount_type',['FLAT', 'PERCENTAGE']);
            $table->double('discount_amt',6,2)->default(0);
            $table->tinyInteger('discount_percent')->default(0);
            $table->double('minimum_order_amt',6,2)->default(0);
            $table->double('max_discount_amt',6,2)->default(0);
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status',['active','inactive'])->default('active');
            $table->timestamps();

            $table->unique(['code','coupon_for']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
