<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_details', function (Blueprint $table) {
            $table->id();
             $table->foreignId('app_user_id')->constrained('app_users')->onDelete('cascade');
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->foreignId('test_mapping_id')->constrained('test_mappings')->onDelete('cascade');
            $table->enum('order_for',['S','F'])->comment('S for Self and F for Family Member');
            $table->foreignId('family_member_id')->nullable()->constrained('family_members')->deleteOnNull();
            $table->double('test_amount',8,2);
            $table->double('net_amount',8,2);
            $table->tinyInteger('order_status')->default(1)->comment('1 - Order Confirmed, 2 - Sample Collected, 3 - Sent to Lab, 4 - Report Generated, 5 - Cancelled');
            $table->timestamp('collection_date_time')->nullable();
            $table->timestamp('sent_to_lab_date_time')->nullable();
            $table->timestamp('report_date_time')->nullable();
            $table->string('test_report',100)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_details');
    }
};
