<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Masters\RoleController;
use App\Http\Controllers\Masters\BloodGroupController;
use App\Http\Controllers\Masters\StateController;
use App\Http\Controllers\Masters\CityController;
use App\Http\Controllers\Masters\AssociationController;
use App\Http\Controllers\Masters\CancelReasonController;
use App\Http\Controllers\ManagerController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\FaqController;
use App\Http\Controllers\PdfController;
use App\Http\Controllers\BookingStepController;

Route::get('/', function () {
    // return view('welcome');
    return to_route('login');
});

Route::prefix('admin')->group(function(){
    Route::get('login',[AuthController::class,'login'])->name('login');
    Route::post('login',[AuthController::class,'loginSubmit'])->name('loginSubmit');

    Route::middleware('auth')->group(function () {
        Route::get('dashboard',[AuthController::class,'dashboard'])->name('dashboard');
        Route::get('logout',[AuthController::class,'logout'])->name('logout');

        // Settings
        Route::get('settings',[SettingController::class,'index'])->name('settings.index');

        Route::prefix('user')->controller(SettingController::class)->group(function () {
            Route::get('settings-about-us', 'aboutUsUser')->name('settings.aboutUsUser');
            Route::post('settings-about-us', 'updateOrCreateAboutUsUser')->name('settings.updateOrCreateAboutUsUser');

            Route::get('settings-privacy-policy', 'privacyPolicyUser')->name('settings.privacyPolicyUser');
            Route::post('settings-privacy-policy', 'updateOrCreatePrivacyPolicyUser')->name('settings.updateOrCreatePrivacyPolicyUser');

            Route::get('settings-terms-conditions', 'termsConditionsUser')->name('settings.termsConditionsUser');
            Route::post('settings-terms-conditions', 'updateOrCreateTermsConditionsUser')->name('settings.updateOrCreateTermsConditionsUser');

            Route::get('settings-help-support', 'helpSupportUser')->name('settings.helpSupportUser');
            Route::post('settings-help-support', 'updateOrCreateHelpSupportUser')->name('settings.updateOrCreateHelpSupportUser');

            Route::get('settings-reward-point-guidelines', 'rewardPointGuidelinesUser')->name('settings.rewardPointGuidelinesUser');
            Route::post('settings-reward-point-guidelines', 'updateOrCreateRewardPointGuidelinesUser')->name('settings.updateOrCreateRewardPointGuidelinesUser');
        });

        Route::prefix('setting')->controller(SettingController::class)->group(function () {
            Route::resource('faqs', FaqController::class);
            Route::resource('booking-steps', BookingStepController::class);
        });

        // Masters
        Route::resource('roles',RoleController::class);
        ROute::resource('blood-groups',BloodGroupController::class);
        Route::resource('states',StateController::class);
        Route::resource('cities',CityController::class);
        Route::resource('associations',AssociationController::class);
        Route::resource('cancel-reasons',CancelReasonController::class)->only(['index', 'store', 'update']);

        Route::view('upload-report/{report_id}','admin.test.upload-report')->name('uploadReport');

        Route::resource('managers',ManagerController::class);
    });
});

//Test Pdf
Route::get('test-pdf',[PdfController::class,'testPdf']);