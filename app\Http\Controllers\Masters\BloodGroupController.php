<?php

namespace App\Http\Controllers\Masters;

use App\Models\Masters\BloodGroup;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BloodGroupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $records = BloodGroup::get();
        return view('admin.masters.blood-groups.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:20|unique:blood_groups,name',
            'status' => 'required|in:active,inactive',
        ]);

        BloodGroup::create([
            'name' => $request->name,
            'status' => $request->status,
        ]);

        return redirect()->route('blood-groups.index')->with('success', 'Blood group created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(BloodGroup $bloodGroup)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BloodGroup $bloodGroup)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BloodGroup $bloodGroup)
    {
        $request->validate([
            'name' => 'required|string|max:20|unique:blood_groups,name,' . $bloodGroup->id,
            'status' => 'required|in:active,inactive',
        ]);

        $bloodGroup->update([
            'name' => $request->name,
            'status' => $request->status,
        ]);

        return redirect()->route('blood-groups.index')->with('success', 'Blood group updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BloodGroup $bloodGroup)
    {
        //
    }
}
