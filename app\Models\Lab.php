<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Masters\State;
use App\Models\Masters\City;

class Lab extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'state_id',
        'city_id',
        'address',
        'logo',
        'header_image',
        'manager_name',
        'manager_email',
        'manager_phone',
        'sequence',
        'status',
    ];

    public function state(){
        return $this->belongsTo(State::class);
    }

    public function city(){
        return $this->belongsTo(City::class);
    }

    public function getLogoUrlAttribute(){
        return file_exists(public_path('uploads/labs/' . $this->logo)) && !empty($this->logo)
            ? asset('uploads/labs/' . $this->logo) 
            : null;
    }
}
