<?php

namespace App\Http\Controllers;

use App\Models\Faq;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $records = Faq::paginate(config('app.page_limit'));
        return view('admin.faq.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'type' => 'required|in:USER,CENTER',
            'question' => 'required|',
            'answer' => 'required',
            'sequence' => 'required|numeric',
            'status' => 'required|in:active,inactive',
        ]);
        try {
            Faq::create([
                'type' => sanitizeForCSV($request->type),
                'question' => sanitizeForCSV($request->question),
                'answer' => sanitizeForCSV($request->answer),
                'sequence' => sanitizeForCSV($request->sequence),
                'status' => sanitizeForCSV($request->status),
            ]);
            return redirect()->route('faqs.index')->with('success', 'FAQ created successfully');
        } catch (\exception $ex) {
            Log::error('faq store', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line_no' => __LINE__,
            ]);
            return redirect()->route('faqs.index')->with('error', 'Something went wrong');
        }
        
    }

    /**
     * Display the specified resource.
     */
    public function show(Faq $faq)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Faq $faq)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Faq $faq)
    {
        $request->validate([
            'type' => 'required|in:USER,CENTER',
            'question' => 'required|',
            'answer' => 'required',
            'sequence' => 'required|numeric',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $faq->update([
                'type' => sanitizeForCSV($request->type),
                'question' => sanitizeForCSV($request->question),
                'answer' => sanitizeForCSV($request->answer),
                'sequence' => sanitizeForCSV($request->sequence),
                'status' => sanitizeForCSV($request->status),
            ]);
            return redirect()->route('faqs.index')->with('success', 'FAQ updated successfully');
        } catch (\exception $ex) {
            Log::error('faq update', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line_no' => __LINE__,
            ]);
            return redirect()->route('faqs.index')->with('error', 'Something went wrong');
        }   
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Faq $faq)
    {
        try {
            $faq->delete();
            return redirect()->route('faqs.index')->with('success', 'FAQ deleted successfully');
        } catch (\exception $ex) {
            Log::error('faq destroy', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line_no' => __LINE__,
            ]);
            return redirect()->route('faqs.index')->with('error', 'Something went wrong');
        }
    }
}
