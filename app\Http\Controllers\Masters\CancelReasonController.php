<?php

namespace App\Http\Controllers\Masters;

use App\Models\Masters\CancelReason;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CancelReasonController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $records = CancelReason::latest()
            ->when(request('name'), function ($query) {
                $query->where('name', 'like', '%' . request('name') . '%');
            })
            ->when(request('status'), function ($query) {
                $query->where('status', request('status'));
            })
            ->paginate(10);
        return view('admin.masters.cancel_reasons.index', compact('records'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:cancel_reasons,name',
            'status' => 'required|in:active,inactive',
        ]);

        CancelReason::create($request->only('name', 'status'));

        return redirect()->route('cancel-reasons.index')->with('success', 'Cancel reason created successfully.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CancelReason $cancelReason)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:cancel_reasons,name,' . $cancelReason->id,
            'status' => 'required|in:active,inactive',
        ]);

        $cancelReason->update($request->only('name', 'status'));

        return redirect()->route('cancel-reasons.index')->with('success', 'Cancel reason updated successfully.');
    }
}
