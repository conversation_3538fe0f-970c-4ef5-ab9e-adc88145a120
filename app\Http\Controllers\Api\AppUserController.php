<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AppUser;
use App\Models\Prescription;
use App\Models\FamilyMember;
use App\Models\AppUserAddress;
use App\Models\Opinion;
use App\Models\NewCart;
use App\Models\Test;
use App\Models\TestMapping;
use App\Models\TestComponent;
use App\Models\Masters\Parameter;
use Illuminate\Http\Request;
use App\Models\CallbackRequest;
use App\Models\BookingStep;
use App\Models\Faq;
use App\Models\Slider;
use App\Models\Coupon;
use App\Models\Setting;
use App\Models\AppVersion;
use App\Models\Lab;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class AppUserController extends Controller
{
    public function labs(Request $request){
        try{
            $labs = Lab::with('state:id,name', 'city:id,name')
                ->when($request->has('id'), function ($query) use ($request) {
                    $query->where('id', $request->id);
                })
                ->where('city_id', $request->user()->city_id)
                ->where('status','active')->get()
                ->map(function($row){
                    $row->logo_url = $row->logo_url;
                    return $row;
                });

            return response()->json(['status'=>true, 'message'=>'Lab Data', 'data'=>$labs], 200);
        }catch(Exception $ex){
            Log::error('Lap fetch error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => $ex->getLine()
            ]);
            return response()->json(['status'=>false, 'message'=>'Something went wrong!'], 500);
        }
        
    }

    public function bloodDoner(Request $request){
        $appUser = AppUser::with('state:id,name','city:id,name','bloodGroup:id,name')
            ->when($request->has('state_id'), function ($query) use ($request) {
                $query->where('state_id', $request->state_id);
            })
            ->when($request->has('city_id'), function ($query) use ($request) {
                $query->where('city_id', $request->city_id);
            })
            ->when($request->has('blood_group_id'), function ($query) use ($request) {
                $query->where('blood_group_id', $request->blood_group_id);
            })
            ->find($request->user()->id);
        $appUsers = AppUser::with('state:id,name','city:id,name','bloodGroup:id,name')
            ->when($request->has('id'), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->when($request->has('state_id'), function ($query) use ($request) {
                $query->where('state_id', $request->state_id);
            })
            ->when($request->has('city_id'), function ($query) use ($request) {
                $query->where('city_id', $request->city_id);
            })
            ->when($request->has('blood_group_id'), function ($query) use ($request) {
                $query->where('blood_group_id', $request->blood_group_id);
            })
            ->where('blood_doner_status','visible')
            ->get();
        $data = [
            'app_user' => $appUser,
            'app_users' => $appUsers,
        ];

        return response()->json(['status'=>true, 'message'=>'Blood Doner fetched successfullt!', 'data'=>$data], 200);
    }

    public function bloodDonersStatusUpdate(Request $request){
        $validated = Validator::make($request->all(),[
            'blood_doner_status' => 'required|string|in:hidden,visible',
        ]);
        if ($validated->fails()) {
            return response()->json(['status' => false, 'message'=>'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }
        AppUser::where('id',$request->user()->id)->update([
            'blood_doner_status'=>$request->blood_doner_status,
        ]);
        return response()->json(['status'=>true, 'message'=>'Status updated successfully!']);
    }

    public function prescriptionsIndex(Request $request){
        $prescriptions = Prescription::select('id','filename','response','created_at')
            ->where('app_user_id',$request->user()->id)
            ->get()
            ->map(function($row){
                $row->filename_url = $row->filename_url;
                return $row;
            });
        return response()->json(['status'=>true, 'message'=>'Prescriptions fetched successfullt!', 'data'=>$prescriptions], 200);
    }

    public function prescriptionsStore(Request $request){
        $validated = Validator::make($request->all(), [
            'filename' => 'required|mimes:jpg,jpeg,png,webp|max:2048',
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        if(isset($request->filename)){
            $filename = date('dmYHis') . rand(111111,999999) . '.' . $request->filename->getClientOriginalExtension();
            $request->file('filename')->move(public_path('uploads/app_users_prescriptions/'),$filename);
        }

        Prescription::create([
            'app_user_id' => $request->user()->id,
            'filename' => $filename
        ]);
        return response()->json(['status' => true, 'message' => 'Prescription added successfully'], 200);
    }

    public function familyMembersIndex(Request $request){
        $familyMembers = FamilyMember::with('appUser:id,name','relation:id,name', 'bloodGroup:id,name')
            ->where('app_user_id',$request->user()->id)
            ->get()
            ->map(function($row){
                $row->age = Carbon::parse($row->dob)->diff(Carbon::now())->format('%y years, %m months, %d days');
                return $row;
            });
        return response()->json(['status'=>true, 'message'=>'Family Member fetched successfullt!', 'data'=>$familyMembers], 200);
    }

    public function familyMembersStore(Request $request){
        $validated = Validator::make($request->all(), [
            'name' => 'required|max:100|string',
            'gender' => 'required|string|max:20',
            'dob' => 'required|date',
            'relation_id' => 'required|exists:relations,id',
            'blood_group_id' => 'required|exists:blood_groups,id'
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        FamilyMember::create([
            'app_user_id' => $request->user()->id,
            'name' => $request->name,
            'gender' => $request->gender,
            'dob' => date('Y-m-d', strtotime($request->dob)), //23-03-2000
            'relation_id' => $request->relation_id,
            'blood_group_id' => $request->blood_group_id,
        ]);
        return response()->json(['status' => true, 'message' => 'Family Member added successfully'], 200);
    }

    public function familyMembersDestroy(Request $request, $id){
        FamilyMember::where('app_user_id',$request->user()->id)
            ->where('id',$id)
            ->delete();
        return response()->json(['status' => true, 'message' => 'Family Member deleted successfully'], 200);
    }

    public function addressesIndex(Request $request){
        $appUserAddresses = AppUserAddress::with('appUser:id,name','state:id,name', 'city:id,name')
            ->where('app_user_id',$request->user()->id)
            ->get();
        return response()->json(['status'=>true, 'message'=>'App User Addresses fetched successfullt!', 'data'=>$appUserAddresses], 200);
    }

    public function addressesStore(Request $request){
        $validated = Validator::make($request->all(), [
            'name' => 'required|max:100|string',
            'mobile' => 'required|numeric|digits:10',
            'house_no' => 'required|string|max:50',
            'area' => 'required|string|max:100',
            'landmark' => 'required|string|max:100',
            'pin_code' => 'required|numeric|digits:6',
            'geo_location_latitude' => 'required|string|max:50',
            'geo_location_longitude' => 'required|string|max:50',
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
            'type' => 'required|string|in:Home,Work,Other',
            'use_for_billing' => 'nullable|in:0,1',
            'status' => 'nullable|string|in:active,inactive',
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        AppUserAddress::create([
            'app_user_id' => $request->user()->id,
            'name' => $request->name,
            'mobile' => $request->mobile,
            'house_no' => $request->house_no,
            'area' => $request->area,
            'landmark' => $request->landmark,
            'pin_code' => $request->pin_code,
            'geo_location_latitude' => $request->geo_location_latitude,
            'geo_location_longitude' => $request->geo_location_longitude,
            'state_id' => $request->state_id,
            'city_id' => $request->city_id,
            'type' => $request->type,
            'use_for_billing' => $request->use_for_billing ?? '0',
            'status' => $request->status ?? 'active',
        ]);
        return response()->json(['status' => true, 'message' => 'App User Address added successfully'], 200);
    }

    public function addressesUpdate(Request $request){
        $validated = Validator::make($request->all(), [
            'name' => 'required|max:100|string',
            'mobile' => 'required|numeric|digits:10',
            'house_no' => 'required|string|max:50',
            'area' => 'required|string|max:100',
            'landmark' => 'required|string|max:100',
            'pin_code' => 'required|numeric|digits:6',
            'geo_location_latitude' => 'required|string|max:50',
            'geo_location_longitude' => 'required|string|max:50',
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
            'type' => 'required|string|in:Home,Work,Other',
            'use_for_billing' => 'nullable|in:0,1',
            'status' => 'nullable|string|in:active,inactive',
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        AppUserAddress::where('app_user_id', $request->user()->id)->where('id',$request->id)->update([
            'name' => $request->name,
            'mobile' => $request->mobile,
            'house_no' => $request->house_no,
            'area' => $request->area,
            'landmark' => $request->landmark,
            'pin_code' => $request->pin_code,
            'geo_location_latitude' => $request->geo_location_latitude,
            'geo_location_longitude' => $request->geo_location_longitude,
            'state_id' => $request->state_id,
            'city_id' => $request->city_id,
            'type' => $request->type,
            'use_for_billing' => $request->use_for_billing ?? '0',
            'status' => $request->status ?? 'active',
        ]);
        return response()->json(['status' => true, 'message' => 'App User Address updated successfully'], 200);
    }

    public function addressesDestroy(Request $request, $id){
        AppUserAddress::where('app_user_id',$request->user()->id)
            ->where('id',$id)
            ->delete();
        return response()->json(['status' => true, 'message' => 'App User Address deleted successfully'], 200);
    }

    public function opinionsIndex(Request $request){
        $opinions = Opinion::with('appUser:id,name')
            ->where('app_user_id',$request->user()->id)
            ->latest()
            ->get();
        return response()->json(['status'=>true, 'message'=>'Opinions fetched successfullt!', 'data'=>$opinions], 200);
    }

    public function opinionsStore(Request $request){
        $validated = Validator::make($request->all(), [
            'type' => 'required|string|in:FEEDBACK,SUGGESTION,COMPLAIN',
            'message' => 'required|string',
            'rating' => 'required|string|in:1,2,3,4,5',
        ]);
        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        Opinion::create([
            'app_user_id' => $request->user()->id,
            'type' => $request->type,
            'message' => $request->message,
            'rating' => $request->rating ?? '0',
        ]);
        return response()->json(['status' => true, 'message' => 'Opinion added successfully'], 200);
    }

    public function happyCustomers(){
        try {
            $happyCustomers = Opinion::with('appUser:id,name')->select('id','app_user_id','message','rating')
                ->where('type','FEEDBACK')
                ->where('app_visibility','1')
                ->where('status','read')
                ->get();
            return response()->json(['status'=>true, 'message'=>'Happy Customers Fetched Successfully!', 'data'=>$happyCustomers], 200);
        } catch (\Exception $ex) {
            Log::error('Happy Customers Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function newCartsIndex(Request $request){
        $newCarts = NewCart::with('test:id,test_name,sld_test_code')
            ->select('id','test_id')
            ->where('app_user_id',$request->user()->id)
            ->get();

        $testIds = $newCarts->pluck('test_id')->toArray();
        $testparameters = TestComponent::select('id','test_id','parameter_id')->whereIn('test_id',$testIds)->get();
        $uniqueParameterIds = $testparameters
            ->pluck('parameter_id')        // Get all parameter IDs
            ->unique()                     // Remove duplicates
            ->values()                     // Reset keys
            ->toArray();                   // Convert to array

            // ✅ Count of required parameters
            $requiredCount = count($uniqueParameterIds);

            // ✅ Group test_id → parameter_id[] and filter those having all required parameters
            $testGrouped = TestComponent::select('test_id', 'parameter_id')
                ->whereIn('parameter_id', $uniqueParameterIds)
                ->get()
                ->groupBy('test_id')
                ->filter(function ($items) use ($uniqueParameterIds, $requiredCount) {
                    $paramIds = $items->pluck('parameter_id')->unique();
                    return $paramIds->intersect($uniqueParameterIds)->count() === $requiredCount;
                })
                ->keys()
                ->toArray();

            // ✅ Final matching test_ids
            $matchingTestIds = $testGrouped;

            //recommended tests
            $recommendedTests = Test::select('id','test_name','sld_test_code')
                ->whereIn('id',$matchingTestIds)
                ->where('is_package','active')
                ->get();

            // labs
            $labIds = Lab::where('city_id',$request->user()->city_id)
                ->where('status','active')
                ->pluck('id')->toArray();

            //test price 
            $testMappings = TestMapping::select('id','lab_id','test_id','user_price','sld_price')
                ->whereIn('test_id',$testIds)
                ->whereIn('lab_id',$labIds)
                ->get()
                ->groupBy(function($item){
                    return $item->lab_id . '-' . $item->test_id;
                });

            $records = [];

            foreach ($labIds as $labId) {
                foreach ($testIds as $testId) {
                    $key = $labId . '-' . $testId;
                    $mapping = $testMappings->get($key)?->first();

                    $records[] = [
                        'labId'     => $labId,
                        'testId'    => $testId,
                        'user_price' => $mapping->user_price ?? null, // cross price 
                        'price'  => $mapping->sld_price ?? null, //sld price or actual price
                    ];
                }
            }

            $data = [
                'newCarts' => $newCarts,
                'recommendedTests' => $recommendedTests,
                'testMappings' => $records,
                'Note' => 'user_price is cross price and price is sld price or actual price',
            ];

            return response()->json(['status'=>true, 'message'=>'Cart Feched data!','data'=>$data], 200);
    }

    public function newCartsStore(Request $request){
        $validated = Validator::make($request->all(), [
            'test_id' => 'required|exists:tests,id',
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }

        $exist = NewCart::where('app_user_id',$request->user()->id)
            ->where('test_id',$request->test_id)
            ->count();
        if ($exist > 0) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => 'Test already exist in cart!'], 422);
        }

        NewCart::create([
            'app_user_id' => $request->user()->id,
            'test_id' => $request->test_id
        ]);
        return response()->json(['status' => true, 'message' => 'New Cart Added Successfully'], 200);
    }

    public function newCartsDestroy(Request $request, NewCart $newCart){
        NewCart::where('app_user_id',$request->user()->id)
            ->where('id',$newCart->id)
            ->delete();
        return response()->json(['status' => true, 'message' => 'New Cart Test Deleted Successfully'], 200);
    }

    /**
         * @OA\Get(
         *     path="/api/tests",
         *     tags={"Tests"},
         *     summary="Fetch tests",
         *     description="Get a list of diagnostic tests with filters and relationships.",
         *     operationId="getTests",

        *     @OA\Parameter(
        *         name="id",
        *         in="query",
        *         description="Filter by Test ID",
        *         required=false,
        *         @OA\Schema(type="integer", example=1)
        *     ),
        *     @OA\Parameter(
        *         name="fasting_required",
        *         in="query",
        *         description="Filter by fasting requirement (active/inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="active")
        *     ),
        *     @OA\Parameter(
        *         name="is_package",
        *         in="query",
        *         description="Filter by package tests (active/inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="inactive")
        *     ),
        *     @OA\Parameter(
        *         name="is_popular",
        *         in="query",
        *         description="Filter by popular tests (active/inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="active")
        *     ),
        *     @OA\Parameter(
        *         name="is_special",
        *         in="query",
        *         description="Filter by special tests (active/inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="inactive")
        *     ),
        *     @OA\Parameter(
        *         name="status",
        *         in="query",
        *         description="Filter by status (active or inactive)",
        *         required=false,
        *         @OA\Schema(type="string", enum={"active", "inactive"}, example="active")
        *     ),

        *     @OA\Response(
        *         response=200,
        *         description="Successful response",
        *         @OA\JsonContent(
        *             @OA\Property(property="status", type="boolean", example=true),
        *             @OA\Property(property="message", type="string", example="Test fetched successfully!"),
        *             @OA\Property(
        *                 property="data",
        *                 type="array",
        *                 @OA\Items(
        *                     @OA\Property(property="id", type="integer", example=1),
        *                     @OA\Property(property="name", type="string", example="Lipid Profile"),
        *                     @OA\Property(property="fasting_required", type="string", enum={"active", "inactive"}, example="active"),
        *                     @OA\Property(property="is_package", type="string", enum={"active", "inactive"}, example="inactive"),
        *                     @OA\Property(property="is_popular", type="string", enum={"active", "inactive"}, example="active"),
        *                     @OA\Property(property="is_special", type="string", enum={"active", "inactive"}, example="inactive"),

        *                     @OA\Property(
        *                         property="organ",
        *                         type="object",
        *                         @OA\Property(property="id", type="integer", example=2),
        *                         @OA\Property(property="name", type="string", example="Heart")
        *                     ),
        *                     @OA\Property(
        *                         property="category",
        *                         type="object",
        *                         @OA\Property(property="id", type="integer", example=3),
        *                         @OA\Property(property="name", type="string", example="Blood Test")
        *                     ),
        *                     @OA\Property(
        *                         property="condition",
        *                         type="object",
        *                         @OA\Property(property="id", type="integer", example=5),
        *                         @OA\Property(property="name", type="string", example="Diabetes")
        *                     ),
        *                     @OA\Property(
        *                         property="speciality",
        *                         type="object",
        *                         @OA\Property(property="id", type="integer", example=4),
        *                         @OA\Property(property="name", type="string", example="Cardiology")
        *                     )
        *                 )
        *             )
        *         )
        *     )
        * )
    */

    public function tests(Request $request){ 
        $tests = Test::with('organ:id,name','category:id,name','condition:id,name','speciality:id,name')
            ->when(isset($request->id), function ($query) use ($request) {
                $query->where('id', $request->id);
            })
            ->when(isset($request->fasting_required), function ($query) use ($request) {
                $query->where('fasting_required', $request->fasting_required);
            })
            ->when(isset($request->is_package), function ($query) use ($request) {
                $query->where('is_package', $request->is_package);
            })
            ->when(isset($request->is_popular), function ($query) use ($request) {
                $query->where('is_popular', $request->is_popular);
            })
            ->when(isset($request->is_special), function ($query) use ($request) {
                $query->where('is_special', $request->is_special);
            })
            ->when(isset($request->category_id), function ($query) use ($request) {
                $query->where('category_id', $request->category_id);
            })

            // filter in dashboard search
            ->when(isset($request->search), function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('test_name', 'like', '%' . $request->search . '%')
                    ->orWhereHas('category', function ($cat) use ($request) {
                        $cat->where('name', 'like', '%' . $request->search . '%');
                    });
                });
            })

            ->where('status','active')
            ->get()->map(function($row)  use ($request) {
                $parameterIds = TestComponent::where('test_id',$row->id)->pluck('parameter_id')->filter();
                $row->covered_parameters = count($parameterIds);
                $row->parameters = Parameter::select('id','title')->whereIn('id',$parameterIds)->get();
                $newCart = NewCart::where('app_user_id', $request->user()->id)
                    ->where('test_id', $row->id)
                    ->latest()
                    ->first();
                if(empty($newCart)){
                    $row->cart_exist = "no";
                    $row->new_cart_id = null;
                }else{
                    $row->cart_exist = 'yes';
                    $row->new_cart_id = $newCart->id;
                }
                return $row;
            });

        return response()->json(['status'=>true, 'message'=>'Test fetched successfullt!', 'data'=>$tests], 200);
    }

    public function testsPriceComparisions(Request $request){
        $validated = Validator::make($request->all(),[
            'lab_ids' => 'array|required',
        ]);

        if ($validated->fails()) {
            return response()->json(['status' => false, 'message' => 'Validation Error!', 'error' => $validated->errors()->first()], 422);
        }
    
        try {
            $testIds = NewCart::where('app_user_id', $request->user()->id)
                ->pluck('test_id');

            $testMappings = TestMapping::whereIn('test_id', $testIds)
                ->whereIn('lab_id', $request->lab_ids)
                ->get()
                ->groupBy(function ($item) {
                    return $item->lab_id . '-' . $item->test_id;
                });

            $records = [];

            foreach ($request->lab_ids as $labId) {
                foreach ($testIds as $testId) {
                    $key = $labId . '-' . $testId;
                    $mapping = $testMappings->get($key)?->first();

                    $records[] = [
                        'lab_id'     => $labId,
                        'test_id'    => $testId,
                        'user_price' => $mapping->user_price ?? null,
                        'sld_price'  => $mapping->sld_price ?? null,
                    ];
                }
            }
            
            return response()->json(['status'=>true, 'message'=>'Test Price Comparisions Fetched Successfully!', 'data'=>$records], 200);
        } catch (\Exception $ex) {
            Log::error('Test Price Comparisions Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function callbackRequestStore(Request $request){
        CallbackRequest::create([
            'app_user_id' => $request->user()->id,
        ]);
        return response()->json(['status'=>true, 'message'=>'You will receive callback afeter 30 minute!'], 200);
    }

    public function aboutUs(){
        try {
            $aboutUs = Setting::where('key_name','user_about_us')->pluck('value')->first();
            return response()->json(['status'=>true, 'message'=>'User About Us Fetched Successfully!', 'data'=>$aboutUs], 200);
        } catch (\Exception $ex) {
            Log::error('User About Us Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function termsAndConditions(){
        try {
            $termsAndConditions = Setting::where('key_name','user_terms_conditions')->pluck('value')->first();
            return response()->json(['status'=>true, 'message'=>'User Terms And Conditions Fetched Successfully!', 'data'=>$termsAndConditions], 200);
        } catch (\Exception $ex) {
            Log::error('User Terms And Conditions Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function privacyPolicy(){
        try {
            $privacyPolicy = Setting::where('key_name','user_privacy_policy')->pluck('value')->first();
            return response()->json(['status'=>true, 'message'=>'User Privacy Policy Fetched Successfully!', 'data'=>$privacyPolicy], 200);
        } catch (\Exception $ex) {
            Log::error('User Privacy Policy Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function helpSupport(){
        try {   
            $helpSupport = Setting::where('key_name','user_help_support')->pluck('value')->first();
            return response()->json(['status'=>true, 'message'=>'User Help Support Fetched Successfully!', 'data'=>$helpSupport], 200);
        } catch (\Exception $ex) {
            Log::error('User Help Support Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function bookingSteps(){
        try {
            $bookingSteps = BookingStep::select('id','sequence','title','description')
                ->where('type','app-user')
                ->orderByRaw('ISNULL(sequence), sequence ASC')
                ->get();
            return response()->json(['status'=>true, 'message'=>'Booking Steps Fetched Successfully!', 'data'=>$bookingSteps], 200);
        } catch (\Exception $ex) {
            Log::error('Booking Steps Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function faqs(){
        try {
            $faqs = Faq::select('id','sequence','question','answer')
                ->where('type','app-user')
                ->orderByRaw('ISNULL(sequence), sequence ASC')
                ->get();
            return response()->json(['status'=>true, 'message'=>'Faq Fetched Successfully!', 'data'=>$faqs], 200);
        } catch (\Exception $ex) {
            Log::error('Faq Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function sliders(){
        try {
            $sliders = Slider::select('id','title','image','sequence',)
                ->where('type','app-user')
                ->orderByRaw('ISNULL(sequence), sequence ASC')
                ->get()
                ->map(function($row){{ 
                    $row->img = $row->img;
                    return $row;
                 }});
            return response()->json(['status'=>true, 'message'=>'Slider Fetched Successfully!', 'data'=>$sliders], 200);
        } catch (\Exception $ex) {
            Log::error('Slider Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function coupons(){
        try {
            $coupons = Coupon::where('coupon_for','USER')
                ->where('start_date', '<=', date('Y-m-d'))
                ->where('end_date', '>=', date('Y-m-d'))
                ->where('status','active')
                ->get();
            return response()->json(['status'=>true, 'message'=>'Coupon Fetched Successfully!', 'data'=>$coupons], 200);
        } catch (\Exception $ex) {
            Log::error('Coupon Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function appVersion(){
        try {
            $appVersion = AppVersion::select('version','is_mandatory')->where('type','USER')->latest()->first();
            $appVersion->is_mandatory_update = $appVersion->is_mandatory_update;
            return response()->json(['status'=>true, 'message'=>'User App Version Fetched Successfully!', 'data'=>$appVersion], 200);
        } catch (\Exception $ex) {
            Log::error('App Version Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }
}