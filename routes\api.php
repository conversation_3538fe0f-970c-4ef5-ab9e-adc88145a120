<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\FetchController;

use App\Http\Controllers\Api\AuthAppUserController;
use App\Http\Controllers\Api\AppUserController;
use App\Http\Controllers\Api\AppUserRewardPointController;
use App\Http\Controllers\Api\AppUser\AppUserOrderController;

use App\Http\Controllers\Api\AuthCollectionCenterController;
use App\Http\Controllers\Api\CollectionCenters\CollectionCenterController;

// Fetch Data
Route::controller(FetchController::class)->group(function () {
    Route::get('categories', 'categories');
    Route::get('states', 'states');
    Route::get('cities', 'cities');
    Route::get('organs', 'organs');
    Route::get('containers','containers');
    Route::get('conditions','conditions');
    Route::get('specialities','specialities');
    Route::get('tests','tests');
    Route::get('labs','labs');
    Route::get('collection-centers','collectionCenters');
    Route::get('components','components');
    Route::get('parameters','parameters');
    Route::get('relations','relations');
    Route::get('services','services');
    Route::get('blood-groups','bloodGroups');
    Route::get('test-methods','testMethods');
    Route::get('app-details','appDetails');
});

// App User
Route::prefix('app-users')->group(function () {
    Route::post('register-login-otp', [AuthAppUserController::class, 'registerLoginOtp']);
    Route::post('otp-verify', [AuthAppUserController::class, 'otpVerify']);
    // App Version
    Route::get('app-version',[AppUserController::class,'appVersion']);

    Route::middleware('auth:app_users')->group(function () {
        // Authentication
        Route::controller(AuthAppUserController::class)->group(function () {
            Route::get('profile', 'profile');
            Route::post('update-register-profile1', 'updateRegisterProfile1');
            Route::post('update-profile', 'updateProfile');
            Route::get('logout', 'logout');
        });

        Route::controller(AppUserController::class)->group(function () {
            // Labs
            Route::get('labs','labs');

            // Blood Donors
            Route::get('blood-doners', 'bloodDoner');
            Route::post('blood-doners-status-update', 'bloodDonersStatusUpdate');

            // Prescriptions
            Route::get('prescriptions', 'prescriptionsIndex');
            Route::post('prescriptions', 'prescriptionsStore');

            // Family Members
            Route::get('family-members', 'familyMembersIndex');
            Route::post('family-members', 'familyMembersStore');
            Route::get('family-members-destroy/{id}', 'familyMembersDestroy');

            // App user Addresses 
            Route::get('addresses', 'addressesIndex');
            Route::post('addresses', 'addressesStore');
            Route::post('addresses-update', 'addressesUpdate');
            Route::get('addresses-destroy/{id}', 'addressesDestroy');

            // Opinion
            Route::get('opinions', 'opinionsIndex');
            Route::post('opinions', 'opinionsStore');
            Route::get('happy-customers','happyCustomers');

            // Cart
            Route::get('new-carts', 'newCartsIndex');
            Route::post('new-carts', 'newCartsStore');
            Route::get('new-carts-destroy/{newCart}', 'newCartsDestroy');

            // tests
            Route::get('tests','tests');
            Route::post('tests-price-comparisions','testsPriceComparisions');
            Route::get('recommended-packages-tests','recommendedPackagesTests');

            // Callback
            Route::post('callback-requests','callbackRequestStore');

            // Other
            Route::get('booking-steps','bookingSteps');
            Route::get('faqs','faqs');
            Route::get('terms-and-conditions','termsAndConditions');
            Route::get('privacy-policy','privacyPolicy');
            Route::get('about-us','aboutUs');
            Route::get('help-support','helpSupport');

            // Sliders
            Route::get('sliders','sliders');

            // Coupons
            Route::get('coupons','coupons');
        });

        Route::get('rewards',[AppUserRewardPointController::class, 'index']);

        Route::controller(AppUserOrderController::class)->group(function () {
            Route::get('members','members');
            Route::get('slots','slots');

            Route::get('orders','index');
            Route::post('orders','store');
        });
    });
});

// Collection center
Route::prefix('collection-centers')->group(function () {
    Route::post('login',[AuthCollectionCenterController::class,'login']);
    // App Version
    Route::get('app-version',[CollectionCenterController::class,'appVersion']);

    Route::middleware('auth:collection_centers')->group(function(){
        Route::controller(AuthCollectionCenterController::class)->group(function () {
            // Authentication
            Route::get('profile', 'profile');
            Route::post('profile-image-update', 'profileImageUpdate');
            Route::post('profile-update', 'profileUpdate');
            Route::post('change-password','changePassword');
            Route::get('logout', 'logout');
        });

        Route::controller(CollectionCenterController::class)->group(function () {
            //
        });
    });
});