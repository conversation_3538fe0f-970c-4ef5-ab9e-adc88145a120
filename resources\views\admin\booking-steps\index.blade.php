@extends('layouts.admin')

@section('content')
    <x-alert />

    {{-- Content Section --}}
    <div class="container-fluid py-1 min-vh-100">

        {{-- Page Header Card --}}
        <div class="card border-0 shadow rounded-4 bg-white mb-4">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center py-3 px-4">
                <div>
                    <h5 class="mb-0 fw-semibold">Booking Steps Management</h5>
                    <small class="text-muted">Manage user Booking Steps — create, edit Booking Steps.</small>
                </div>
                <a href="#" class="btn btn-primary d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addBookingModal">
                    <i class="fa fa-plus me-2"></i> Add Booking Steps
                </a>
            </div>
        </div>

        {{-- Booking Steps Table Card --}}
        <div class="card border-0 shadow bg-white">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center py-3 px-4">
                <h5 class="mb-0 fw-semibold">🎯 Booking Steps List</h5>
                <input type="text" class="form-control form-control-sm w-auto" placeholder="🔍 Search Booking Steps..." id="searchInput">
            </div>

            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover" id="bookingTable">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center">SN.</th>
                                <th class="text-start">Type</th>
                                <th class="text-start">Title</th>
                                <th class="text-start">Description</th>
                                <th class="text-start">Sequence</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($records as $row)
                                <tr>
                                    <td class="text-center text-muted">{{ $loop->iteration }}</td>
                                    <td class="text-start">{{ $row->type ?? '' }}</td>
                                    <td class="text-start">{{ $row->title ?? '' }}</td>
                                    <td class="text-start">{{ $row->description ?? '' }}</td>
                                    <td class="text-start">{{ $row->sequence ?? '' }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ $row->status == 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($row->status ?? '') }}
                                        </span>
                                    </td>
                                    <td class="d-flex justify-content-center text-center">
                                        <a href="#" class="btn btn-sm btn-outline-warning me-1" title="Edit" data-bs-toggle="modal" data-bs-target="#editBookingModal{{ $row->id }}">
                                            <i class="fa fa-edit"></i>
                                        </a>
                                        <form action="{{ route('booking-steps.destroy', $row->id) }}" method="POST" id="delete-form{{ $row->id }}" class="m-0 p-0">
                                            @csrf
                                            @method('DELETE')
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="Delete" onclick="confirmDelete({{ $row->id }})">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fa fa-circle-exclamation me-2 text-secondary"></i> No Booking Steps found.
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                    <div class="mt-3 px-3">
                        {{ $records->links('pagination::bootstrap-5') }}
                    </div>
                </div>
            </div>
        </div>

    </div>

    {{-- Add Booking Steps Modal --}}
    <div class="modal fade" id="addBookingModal" tabindex="-1" aria-labelledby="addFaqModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addFaqModalLabel">Add New Booking Steps <i class="fa fa-plus"></i></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('booking-steps.store') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <input type="hidden" name="form_type" value="create">
                        <div class="mb-3">
                            <label for="booking_type" class="form-label">Type <span class="text-danger">*</span></span></label>
                            <select class="form-select" name="type" id="booking_type" required>
                                <option value="USER">USER</option>
                                <option value="CENTER">CENETR</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="faq_title" class="form-label">Title <span class="text-danger">*</span></span></label>
                            <input type="text" class="form-control" id="faq_title" name="title" maxlength="255" value="{{ old('title') }}" required placeholder="Enter Booking Steps title">
                        </div>  
                        <div class="mb-3">
                            <label for="faq_answer" class="form-label">Description <span class="text-danger">*</span></span></label>
                            <textarea class="form-control" id="faq_answer" name="description" required placeholder="Enter Booking Steps description">{{ old('description') }}</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="faq_sequence" class="form-label">Sequence </label>
                            <input type="number" class="form-control" id="faq_sequence" name="sequence" min="1" value="{{ old('sequence') }}" placeholder="Enter Booking Steps sequence">
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="active" @selected(old('status') == 'active')>Active</option>
                                <option value="inactive" @selected(old('status') == 'inactive')>Inactive</option>
                            </select>
                        </div>    
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fa fa-times"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-check"></i> Add Booking Steps
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {{-- Edit Booking Steps Modal --}}
    @foreach($records as $row)
    <div class="modal fade" id="editBookingModal{{ $row->id }}" tabindex="-1" aria-labelledby="editModalLabel{{ $row->id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel{{ $row->id }}">Edit Booking Steps <i class="fa fa-edit    "></i></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('booking-steps.update', $row->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="modal-body">
                        <input type="hidden" name="id" value="{{ $row->id }}">
                        <input type="hidden" name="form_type" value="edit" class="hidden">
                        <div class="mb-3">
                            <label for="booking_type" class="form-label">Type <span class="text-danger">*</span></span></label>
                            <select class="form-select" name="type" id="booking_type" required>
                                <option value="USER" {{ old('USER', $row->type == 'USER' ? 'selected' : '') }}>USER</option>
                                <option value="CENTER" {{ old('CENTER', $row->type == 'CENTER' ? 'selected' : '') }}>CENETR</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="edit_title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_title" name="title" value="{{ old('title', $row->title ?? '') }}" required placeholder="Enter Booking Steps title">
                        </div>  
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description <span class="text-danger">*</span></span></label>
                            <textarea class="form-control" id="edit_description" name="description" required placeholder="Enter Booking Steps description">{{ old('description', $row->description ?? '') }}</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="faq_sequence" class="form-label">Sequence</label>
                            <input type="number" class="form-control" id="faq_sequence" name="sequence" min="1" value="{{ old('sequence', $row->sequence ?? '') }}" placeholder="Enter Booking Steps sequence">
                        </div>
                        <div class="mb-3">
                            <label for="edit_status" class="form-label">Status</label>
                            <select class="form-select" id="edit_status" name="status" required>
                                <option value="active" {{ old('active', $row->status == 'active' ? 'selected' : '') }}>Active</option>
                                <option value="inactive" {{ old('inactive', $row->status == 'inactive' ? 'selected' : '') }}>Inactive</option>
                            </select>   
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fa fa-times"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-check"></i> Update Booking Steps
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endforeach

@endsection

{{-- Search Filter Script --}}
@push('scripts')
    <script>
        document.getElementById('searchInput').addEventListener('keyup', function () {
            const search = this.value.toLowerCase();
            const rows = document.querySelectorAll('#bookingTable tbody tr');

            rows.forEach(row => {
                const name = row.children[1].textContent.toLowerCase();
                row.style.display = name.includes(search) ? '' : 'none';
            });
        });
    </script>

    @if ($errors->any())
        <script>
            $(document).ready(function () {
                 @if (old('form_type') == 'create')
                    $('#addBookingModal').modal('show');
                @else
                    $('#editBookingModal{{ old('id') }}').modal('show');
                @endif
            });
        </script>
    @endif

    <x-confirm-delete />
@endpush