@extends('layouts.admin')

@section('content')
<x-alert />

{{-- Content Section --}}
<div class="container-fluid py-1 min-vh-100">

    {{-- Page Header Card --}}
    <div class="card border-0 shadow rounded-4 bg-white mb-4">
        <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center py-3 px-4">
            <div>
                <h5 class="mb-0 fw-semibold">Blood Group Management</h5>
                <small class="text-muted">Manage user blood groups — create, edit blood groups.</small>
            </div>
            <a href="#" class="btn btn-primary d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                <i class="fa fa-plus me-2"></i> Add Blood Group
            </a>
        </div>
    </div>

    {{-- Blood Group Table Card --}}
    <div class="card border-0 shadow rounded-4 bg-white">
        <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center py-3 px-4">
            <h5 class="mb-0 fw-semibold">🎯 Blood Group List</h5>
            <input type="text" class="form-control form-control-sm w-auto" placeholder="🔍 Search blood groups..." id="searchInput">
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0" id="rolesTable">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center" style="width: 5%;">SN.</th>
                            <th class="text-start">Name</th>
                            <th class="text-center" style="width: 15%;">Status</th>
                            <th class="text-center" style="width: 20%;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($records as $row)
                            <tr>
                                <td class="text-center text-muted">{{ $loop->iteration }}</td>
                                <td class="text-start">{{ $row->name }}</td>
                                <td class="text-center">
                                    <span class="badge bg-{{ $row->status == 'active' ? 'success' : 'secondary' }}">
                                        {{ ucfirst($row->status) }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <a href="#" class="btn btn-sm btn-outline-warning me-1" title="Edit" data-bs-toggle="modal" data-bs-target="#editRoleModal{{ $row->id }}">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <i class="fa fa-circle-exclamation me-2 text-secondary"></i> No blood groups found.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>

{{-- Add Blood Group Modal --}}
<div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRoleModalLabel">Add New Blood Group <i class="fa fa-plus"></i></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('blood-groups.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">Blood Group Name</label>
                        <input type="text" class="form-control" id="roleName" name="name" maxlength="255" value="{{ old('name') }}" required placeholder="Enter role name">
                    </div>  
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="active" @selected(old('status') == 'active')>Active</option>
                            <option value="inactive" @selected(old('status') == 'inactive')>Inactive</option>
                        </select>
                    </div>    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-check"></i> Add Blood Group
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- Edit Blood Group Modal --}}
@foreach($records as $row)
<div class="modal fade" id="editRoleModal{{ $row->id }}" tabindex="-1" aria-labelledby="editRoleModalLabel{{ $row->id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRoleModalLabel{{ $row->id }}">Edit Blood Group <i class="fa fa-edit    "></i></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('blood-groups.update', $row->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roleName{{ $row->id }}" class="form-label">Blood Group Name</label>
                        <input type="text" class="form-control" id="roleName{{ $row->id }}" name="name" value="{{ $row->name }}" required placeholder="Enter role name">
                    </div>
                    <div class="mb-3">
                        <label for="status{{ $row->id }}" class="form-label">Status</label>
                        <select class="form-select" id="status{{ $row->id }}" name="status" required>
                            <option value="active" {{ $row->status == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ $row->status == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>   
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-check"></i> Update Blood Group
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach

{{-- Search Filter Script --}}
@push('scripts')
    <script>
        document.getElementById('searchInput').addEventListener('keyup', function () {
            const search = this.value.toLowerCase();
            const rows = document.querySelectorAll('#rolesTable tbody tr');

            rows.forEach(row => {
                const name = row.children[1].textContent.toLowerCase();
                row.style.display = name.includes(search) ? '' : 'none';
            });
        });
    </script>
@endpush

@endsection