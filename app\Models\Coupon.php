<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    protected $fillable = [
        'code',
        'title',
        'coupon_for'.
        'discount_type',
        'discount_amt',
        'discount_percent',
        'minimum_order_amt',
        'max_discount_amt',
        'start_date',
        'end_date',
        'status',
    ];

    public function getStartDateAttribute($value){
        return $this->start_date = formatDateToDMY($value);
    } 

    public function getEndDateAttribute($value){
        return $this->start_date = formatDateToDMY($value);
    } 
}
