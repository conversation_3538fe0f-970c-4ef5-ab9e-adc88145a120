<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('phlebos', function (Blueprint $table) {
            $table->id();
            $table->string('name',70);
            $table->string('phone',10)->unique();
            $table->string('email',70)->unique();
            $table->date('dob')->nullable();
            $table->string('gender',10);
            $table->foreignId('blood_group_id')->nullable()->constrained('blood_groups')->onDelete('cascade');
            $table->foreignId('state_id')->constrained('states')->onDelete('cascade');
            $table->foreignId('city_id')->constrained('cities')->onDelete('cascade');
            $table->string('password',100);
            $table->string('current_address');
            $table->string('permanent_address');

            // Work Location
            $table->foreignId('work_state_id')->constrained('states')->onDelete('cascade');
            $table->foreignId('work_city_id')->constrained('cities')->onDelete('cascade');
            $table->string('work_pincode',6);

            $table->string('profile_image',100)->nullable();
            $table->string('referal_code',10)->unique();
            $table->string('refered_by',10)->nullable();
            $table->enum('status',['active','inactive'])->default('active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('phlebos');
    }
};
