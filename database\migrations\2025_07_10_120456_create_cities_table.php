<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cities', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('City Name');
            $table->foreignId('state_id')->constrained('states')->onDelete('cascade')->comment('State ID');
            $table->tinyInteger('public_status')->default(1);
            $table->enum('status',['active','inactive'])->default('active');
            $table->integer('new_request')->default(0)->comment('Number of new requests');
            $table->timestamps();

            $table->unique(['name', 'state_id'], 'unique_city_state');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cities');
    }
};
