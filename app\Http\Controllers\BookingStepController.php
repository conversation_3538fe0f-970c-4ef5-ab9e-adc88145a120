<?php

namespace App\Http\Controllers;

use App\Models\BookingStep;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class BookingStepController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $records = BookingStep::paginate(config('app.page_limit'));
        return view('admin.booking-steps.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'type' => 'required|in:USER,CENTER',
            'title' => 'required|',
            'description' => 'required',
            'sequence' => 'nullable|numeric',
            'status' => 'required|in:active,inactive',
        ]);
        try {
            BookingStep::create([
                'type' => sanitizeForCSV($request->type),
                'title' => sanitizeForCSV($request->title),
                'description' => sanitizeForCSV($request->description),
                'sequence' => $request->sequence ?? null,
                'status' => sanitizeForCSV($request->status),
            ]);
            return redirect()->route('booking-steps.index')->with('success', 'Booking Step Created Successfully');
        } catch (\exception $ex) {
            Log::error('booking steps store', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line_no' => __LINE__,
            ]);
            return redirect()->route('booking-steps.index')->with('error', 'Something Went Wrong');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(BookingStep $bookingStep)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BookingStep $bookingStep)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BookingStep $bookingStep)
    {
        $request->validate([
            'type' => 'required|in:USER,CENTER',
            'title' => 'required|',
            'description' => 'required',
            'sequence' => 'nullable|numeric',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $bookingStep->update([
                'type' => sanitizeForCSV($request->type),
                'title' => sanitizeForCSV($request->title),
                'description' => sanitizeForCSV($request->description),
                'sequence' => $request->sequence ?? null,
                'status' => sanitizeForCSV($request->status),
            ]);
            return redirect()->route('booking-steps.index')->with('success', 'Booking Step Updated Successfully');
        } catch (\exception $ex) {
            Log::error('booking steps update', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line_no' => __LINE__,
            ]);
            return redirect()->route('booking-steps.index')->with('error', 'Something Went Wrong');
        }   
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BookingStep $bookingStep)
    {
        try {
            $bookingStep->delete();
            return redirect()->route('booking-steps.index')->with('success', 'Booking Step Deleted Successfully');
        } catch (\exception $ex) {
            Log::error('booking steps destroy', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line_no' => __LINE__,
            ]);
            return redirect()->route('booking-steps.index')->with('error', 'Something Went Wrong');
        }
    }
}
