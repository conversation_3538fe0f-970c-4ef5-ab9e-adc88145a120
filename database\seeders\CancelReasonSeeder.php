<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use DB;

class CancelReasonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(DB::table('cancel_reasons')->count() > 0) {
            return; 
        }

        DB::table('cancel_reasons')->insert([
            ['name' => 'Other', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Phlebotomist (Sample collector) did not Come for Collection', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Delay for Sample Collection', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Booked Wrong Test', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Patient Not Available', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
        ]);
    }
}
