<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tests', function (Blueprint $table) {
            $table->id();
            $table->string('test_name',100);
            $table->string('test_name_also_known_as',100)->nullable();
            $table->foreignId('organ_id')->nullable()->constrained('organs')->onDelete('set null');
            $table->string('sld_test_code',50)->index();
            $table->string('container_type_ids')->nullable()->comment('comma separated container id');
            $table->text('description');
            $table->string('specimen_details',200);
            $table->text('pre_test_info',100);
            $table->foreignId('category_id')->nullable()->constrained('categories')->onDelete('set null');
            $table->foreignId('condition_id')->nullable()->constrained('conditions')->onDelete('set null');
            $table->foreignId('speciality_id')->nullable()->constrained('specialities')->onDelete('set null');
            $table->text('test_usage')->nullable();
            $table->longText('components',100)->nullable();
            $table->text('remark')->nullable();
            $table->string('report_time',10);
            $table->string('stability_room',30)->nullable();
            $table->string('stability_refrigerator',30)->nullable();
            $table->string('stability_frozen',30)->nullable();
            $table->enum('fasting_required',['active','inactive'])->default('inactive');
            $table->enum('is_package',['active','inactive'])->default('inactive');
            $table->enum('is_popular',['active','inactive'])->default('inactive');
            $table->enum('is_special',['active','inactive'])->default('inactive');
            $table->enum('status',['active','inactive'])->default('active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tests');
    }
};
