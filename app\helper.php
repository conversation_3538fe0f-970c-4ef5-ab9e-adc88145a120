<?php

use App\Models\AppUserRewardPoint;
use App\Models\Order;

if (!function_exists("sanitizeForCSV")) {
    function sanitizeForCSV($data) {
        // Encode special HTML characters
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');

        // Escape leading characters that may trigger formulas in spreadsheet software
        $specialChars = ['=', '+', '-', '@']; 
        if (in_array(substr($data, 0, 1), $specialChars)) {
            // Prefix with a single quote to prevent formula interpretation
            $data = "'" . $data;
        }

        return $data;
    }
}

if (!function_exists('formatDateToDMY')) {
    function formatDateToDMY($date) {
        if (empty($date)) {
            return null;
        }
        return date('d-m-Y', strtotime($date));
    }
}

if (!function_exists('formatDateToDMYHIS')) {
    function formatDateToDMYHIS($date) {
        if (empty($date)) {
            return null;
        }
        return date('d-m-Y H:i A', strtotime($date));
    }
}

if (!function_exists('formatDateToYMD')) {
    function formatDateToYMD($date) {
        if (empty($date)) {
            return null;
        }
        return date('Y-m-d', strtotime($date));
    }
}

if (!function_exists('appUserCurrentReward')){
    function appUserCurrentReward($appUserId){
        $crProints = AppUserRewardPoint::where('app_user_id', $appUserId)
            ->where('type', 'CR')
            ->where('expiry_date', '>=', date('Y-m-d'))
            ->sum('points');

        $drProints = Order::where('app_user_id', $appUserId)
            ->where('booking_status', '!=', 'CANCELLED')
            ->sum('reward_point_used');

        return $crProints - $drProints;
    }
}