@if (session()->has('success'))
    {{-- <div class="alert alert-success" role="alert">
        {{ session('success') }}
    </div> --}}
    <script>
        Swal.fire({
            toast: true,
            position: 'top-end',
            icon: 'success',
            title: "{{ session('success') }}",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
    </script>
@endif
{{-- <script>
    $('.alert-success').slideUp(3000);
</script> --}}

@if (session()->has('error'))
    {{-- <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div> --}}

    <script>
        Swal.fire({
            toast: true,
            position: 'top-end',
            icon: 'error',
            title: "{{ session('error') }}",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
    </script>
@endif

@if ($errors->any())
    {{-- @foreach ($errors->all() as $error) --}}
        {{-- <div class="alert alert-warning alert-dismissible fade show" role="alert">
            {{ $error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div> --}}

        <script>
            Swal.fire({
                icon: 'error',
                title: 'Validation Error!',
                text: "{{ $errors->first() }}",
                confirmButtonText: 'OK'
            });
        </script>
        {{-- @break
    @endforeach --}}
@endif