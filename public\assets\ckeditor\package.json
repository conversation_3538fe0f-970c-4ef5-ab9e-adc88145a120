{"name": "ckeditor5-custom-build", "author": "CKSource", "description": "A custom CKEditor 5 build made by the CKEditor 5 online builder.", "version": "0.0.1", "license": "SEE LICENSE IN LICENSE.md", "private": true, "main": "./build/ckeditor.js", "devDependencies": {"@ckeditor/ckeditor5-adapter-ckfinder": "^38.1.0", "@ckeditor/ckeditor5-alignment": "^38.1.0", "@ckeditor/ckeditor5-autoformat": "^38.1.0", "@ckeditor/ckeditor5-basic-styles": "^38.1.0", "@ckeditor/ckeditor5-block-quote": "^38.1.0", "@ckeditor/ckeditor5-cloud-services": "^38.1.0", "@ckeditor/ckeditor5-dev-translations": "^32.1.2", "@ckeditor/ckeditor5-dev-utils": "^32.1.2", "@ckeditor/ckeditor5-editor-classic": "^38.1.0", "@ckeditor/ckeditor5-essentials": "^38.1.0", "@ckeditor/ckeditor5-find-and-replace": "^38.1.0", "@ckeditor/ckeditor5-font": "^38.1.0", "@ckeditor/ckeditor5-heading": "^38.1.0", "@ckeditor/ckeditor5-highlight": "^38.1.0", "@ckeditor/ckeditor5-horizontal-line": "^38.1.0", "@ckeditor/ckeditor5-html-support": "^38.1.0", "@ckeditor/ckeditor5-image": "^38.1.0", "@ckeditor/ckeditor5-indent": "^38.1.0", "@ckeditor/ckeditor5-link": "^38.1.0", "@ckeditor/ckeditor5-list": "^38.1.0", "@ckeditor/ckeditor5-media-embed": "^38.1.0", "@ckeditor/ckeditor5-paragraph": "^38.1.0", "@ckeditor/ckeditor5-paste-from-office": "^38.1.0", "@ckeditor/ckeditor5-select-all": "^38.1.0", "@ckeditor/ckeditor5-special-characters": "^38.1.0", "@ckeditor/ckeditor5-table": "^38.1.0", "@ckeditor/ckeditor5-theme-lark": "^38.1.0", "@ckeditor/ckeditor5-typing": "^38.1.0", "css-loader": "^5.2.7", "postcss": "^8.4.24", "postcss-loader": "^4.3.0", "raw-loader": "^4.0.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "webpack": "^5.88.0", "webpack-cli": "^4.10.0"}, "scripts": {"build": "webpack --mode production"}}