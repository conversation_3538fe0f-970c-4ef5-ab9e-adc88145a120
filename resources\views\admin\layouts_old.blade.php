<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{{ config('constant.app_name') }} - Login</title>

        {{-- bootstrap css --}}
        <link rel="stylesheet" href="{{ asset('assets/admin/bootstrap/bootstrap.min.css') }}">
        {{-- font awesome --}}
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        {{-- jquery --}}
        <script src="{{ asset('assets/jquery/jquery.min.js') }}"></script>
        
        <link rel="icon" href="{{ asset('logo-img.png') }}" type="image/png">

        <style>
            /* footer down */
            body, hrml {
                height: 100%;
            }
            .wrapper {
                min-height: 100vh;
                display: flex;
                flex-direction: column;
            }
            main {
                flex: 1;
            }

            .navbar1{
                background: white;
            }

            .main-header {
                background: #2A7B9B;
                background: linear-gradient(90deg,rgba(42, 123, 155, 1) 0%, rgba(70, 115, 87, 1) 100%, rgba(237, 221, 83, 1) 100%);
            }

            .header{
                background: #f0f1f1;
            }

            /* Responsive Sidebar */
            #sidebarMenu {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                width: 250px;
                background: #222;
                color: #fff;
                z-index: 1040;
                transition: transform 0.3s ease;
            }
            .main-content {
                margin-left: 250px;
                transition: all 0.3s;
                padding: 20px;
            }
            /* Collapsed sidebar for desktop */
            #sidebarMenu.collapsed {
                transform: translateX(-250px);
            }
            .main-content.collapsed {
                margin-left: 0;
            }
            /* Mobile styles */
            @media (max-width: 767.98px) {
                #sidebarMenu {
                    transform: translateX(-250px);
                }
                #sidebarMenu.show {
                    transform: translateX(0);
                }
                .main-content {
                    margin-left: 0;
                }
                .sidebar-toggle-btn {
                    display: inline-block !important;
                }
            }
            .sidebar-toggle-btn {
                display: none;
                background: none;
                border: none;
                font-size: 2rem;
                margin-right: 1rem;
            }

            .nav-link {
                color: black    ;
            }
        </style>
        {{-- sidebar css --}}
        <style>
            #sidebarMenu .nav-link {
    padding: 10px 15px;
    border-radius: 8px;
    transition: background 0.3s, color 0.3s;
}

#sidebarMenu .nav-link:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

#sidebarMenu .nav-link.active {
    background-color: #e9ecef;
    font-weight: bold;
    color: #0d6efd;
}

        </style>
        @stack('style')
    </head>
    <body>
        {{-- Navbar --}}
        <nav class="navbar navbar-expand-lg sticky-top navbar1 border-bottom py-2 shadow">
            <div class="container">
                <!-- Sidebar Toggle Button for mobile -->
                <button class="sidebar-toggle-btn" id="sidebarToggle" type="button" aria-label="Toggle sidebar">
                    <span class="fa fa-bars"></span>
                </button>
                {{-- <a class="navbar-brand fw-bold" href="/">
                    <img src="{{ asset('logo-img.png') }}" width="50px;" alt="{{ config('constant.app_name') }} Image">
                    {{ config('constant.app_name') }}
                </a> --}}

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar"
                    aria-controls="mainNavbar" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="mainNavbar">
                    {{-- <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">Home</a>
                        </li>
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">Projects</a>
                        </li>
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">Task</a>
                        </li>
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">Status Update</a>
                        </li>
                    </ul> --}}

                    <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"><i class="fa-solid fa-cart-shopping"></i></a>
                        </li>
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"><i class="fa-solid fa-phone-volume"></i></a>
                        </li>
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"><i class="fa-solid fa-prescription"></i></a>
                        </li>
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"><i class="fa-solid fa-list"></i> </a>
                        </li>
                        <li class="nav-item me-3">
                            <a href="#" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"><i class="fa-solid fa-message"></i> </a>
                        </li>
                    </ul>

                    <div class="dropdown">
                        <a href="#" class="d-block link-body-emphasis text-decoration-none dropdown-toggle"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="https://github.com/mdo.png" alt="mdo" width="32" height="32" class="rounded-circle">
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end text-small">
                            <li><a class="dropdown-item" href="#">New project...</a></li>
                            <li><a class="dropdown-item" href="#">Settings</a></li>
                            <li><a class="dropdown-item" href="#">Profile</a></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><a class="dropdown-item" href="#">Sign out</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        {{-- Sidebar --}}
        {{-- <div class="sidebar bg-secondary" id="sidebarMenu">

            <ul class="nav flex-column pt-3">
                <li class="nav-item">
                    <div class="d-flex justify-content-between">
                        <div><a class="nav-link" href="#"><i class="bi bi-house-door"></i> <span>Dashboard</span></a></div>
                        <div>
                            <div class=" me-2">
                                <i class="fa-solid fa-list"  id="sidebarToggle"></i>
                            </div>
                        </div>
                    </div>
                    
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#"><i class="fa-solid fa-user"></i> <span>Users</span></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#"><i class="bi bi-gear"></i> <span>Settings</span></a>
                </li>
            </ul>
        </div> --}}

        {{-- <div class="flex-shrink-0 p-3 bg-light shadow" style="width: 280px" id="sidebarMenu">
            <div class="d-flex justify-content-between">
                <div>
                    <a href="/" class="d-flex align-items-center pb-3 mb-3 link-body-emphasis text-decoration-none border-bottom">
                        <img src="{{ asset('logo-img.png') }}" width="50px;" alt="{{ config('constant.app_name') }} Image">
                        <span class="fs-5 fw-semibold">
                            {{ config('constant.app_name') }}
                        </span>
                    </a>
                </div>
                <div class="">
                    <button class="btn btn-sm btn-primary" id="sidebarToggle1" type="button">
                        <i class="fa-solid fa-list"></i> 
                    </button>
                </div>
            </div>
            <ul class="list-unstyled ps-0">
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-gauge-high"></i> Dashboard</a>
                </li>
                <li class="mb-1 d-none">
                    <button class="text-dark btn btn-toggle d-inline-flex align-items-center rounded border-0 collapsed"
                        data-bs-toggle="collapse" data-bs-target="#home-collapse" aria-expanded="false">
                        Home
                    </button>
                    <div class="collapse" id="home-collapse">
                        <ul class="btn-toggle-nav list-unstyled fw-normal pb-1 small">
                            <li>
                                <a href="#" class="link-body-emphasis d-inline-flex text-decoration-none rounded">Overview</a>
                            </li>
                            <li>
                                <a href="#" class="link-body-emphasis d-inline-flex text-decoration-none rounded">Updates</a>
                            </li>
                            <li>
                                <a href="#" class="link-body-emphasis d-inline-flex text-decoration-none rounded">Reports</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-user"></i> Area Manager</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-user"></i> Phlebo</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-user-doctor"></i> Doctor</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-warehouse"></i> Collection Centers</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-vial"></i> Manage Test</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-vial-virus"></i> Lab Management</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-brands fa-first-order"></i> Manage Orders</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-user"></i> User Management</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-regular fa-file-lines"></i> Reports</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-headset"></i> Help & Support</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-money-bill"></i> Discount & Coupons</a>
                </li>
                <li class="nav-item mt-3">
                    <a href="{{ route('managers.index') }}" class="nav-link"><i class="fa-solid fa-gear"></i> Settings</a>
                </li>
                <li class="border-top my-3"></li>
            </ul>
        </div> --}}

        <!-- Sidebar -->
        <div class="d-flex flex-column flex-shrink-0 p-3 bg-white border-end shadow-sm" style="width: 280px;" id="sidebarMenu">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                <a href="/" class="d-flex align-items-center text-dark text-decoration-none">
                    <img src="{{ asset('logo-img.png') }}" alt="Logo" width="40" class="me-2">
                    <span class="fs-5 fw-semibold">{{ config('constant.app_name') }}</span>
                </a>
                <button class="btn btn-sm btn-light border" id="sidebarToggle1" type="button">
                    <i class="fa-solid fa-bars"></i>
                </button>
            </div>

            <!-- Navigation -->
            <ul class="nav nav-pills flex-column mb-auto">
                <li class="nav-item">
                    <a href="{{ route('managers.index') }}" class="nav-link text-dark">
                        <i class="fa-solid fa-gauge-high me-2"></i> Dashboard
                    </a>
                </li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-user me-2"></i> Area Manager</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-user me-2"></i> Phlebo</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-user-doctor me-2"></i> Doctor</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-warehouse me-2"></i> Collection Centers</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-vial me-2"></i> Manage Test</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-vial-virus me-2"></i> Lab Management</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-brands fa-first-order me-2"></i> Manage Orders</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-user me-2"></i> User Management</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-regular fa-file-lines me-2"></i> Reports</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-headset me-2"></i> Help & Support</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-money-bill me-2"></i> Discount & Coupons</a></li>
                <li><a href="{{ route('managers.index') }}" class="nav-link text-dark"><i class="fa-solid fa-gear me-2"></i> Settings</a></li>
            </ul>

            <hr class="mt-4">
        </div>

        
        {{-- Main Content --}}
        <div class="wrapper main-content" id="mainContent">
            <main class="container1 m-3">

                @yield('content')

            </main>
        </div>

        {{-- Footer --}}
        <footer class="py-3 my-4"> 
            <p class="text-center text-body-secondary">
                &copy; {{ date('Y') }} <a href="{{ config('constant.developed_by_url') }}"> {{ config('constant.developed_by_name') }} </a>. All rights reserved.    
            </p> 
        </footer>

        {{-- bootstrap js --}}
        <script src="{{ asset('assets/admin/bootstrap/bootstrap.min.js') }}"></script>

        {{-- Sidebar toglle --}}
        <script>
            const sidebar = document.getElementById('sidebarMenu');
            const content = document.getElementById('mainContent');
            const toggleBtn = document.getElementById('sidebarToggle');
            const toggleBtn1 = document.getElementById('sidebarToggle1');

            toggleBtn.addEventListener('click', function () {
                if (window.innerWidth < 768) {
                    sidebar.classList.toggle('show');
                } else {
                    sidebar.classList.toggle('collapsed');
                    content.classList.toggle('collapsed');
                }
            });

            toggleBtn1.addEventListener('click', function () {
                if (window.innerWidth < 768) {
                    sidebar.classList.toggle('show');
                } else {
                    sidebar.classList.toggle('collapsed');
                    content.classList.toggle('collapsed');
                }
            });
        </script>

        {{-- sidebar toglle js --}}
        <script>
            // Sidebar toggle
            document.getElementById('sidebarToggle1').addEventListener('click', function () {
                document.getElementById('sidebarMenu').classList.toggle('d-none');
            });
        </script>
    </body>
</html>