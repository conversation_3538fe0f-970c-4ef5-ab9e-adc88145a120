<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_user_reward_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('app_user_id')->constrained('app_users')->onDelete('cascade');
            $table->integer('order_id')->nullable();
            $table->integer('points')->default(0);
            $table->enum('type',['CR','DR'])->default('CR');
            $table->date('expiry_date')->nullable()->comment('+1 year from create date, only added if type is CR');
            $table->enum('remark',['WELCOME', 'REFERRAL', 'ORDER'])->nullable()->comment('Credit Type');
            $table->enum('reason', ['REDEEM', 'EXPIRED'])->nullable()->comment('Reason for debiting points (redeemed or expired)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_user_reward_points');
    }
};
