<?php

namespace App\Http\Controllers\Api\CollectionCenters;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CollectionCenter;
use App\Models\AppVersion;

class CollectionCenterController extends Controller
{
    public function appVersion(){
        try {
            $appVersion = AppVersion::select('version','is_mandatory')->where('type','CENTER')->latest()->first();
            $appVersion->is_mandatory_update = $appVersion->is_mandatory_update;
            return response()->json(['status'=>true, 'message'=>'Collection Center App Version Fetched Successfully!', 'data'=>$appVersion], 200);
        } catch (\Exception $ex) {
            Log::error('App Version Fetch Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status' => false, 'message' => 'Something went wrong!', 'error' => 'Internal Server Error'], 500);
        }
    }
}
