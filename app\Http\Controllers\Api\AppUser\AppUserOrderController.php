<?php

namespace App\Http\Controllers\Api\AppUser;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\FamilyMember;
use App\Models\Slot;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class AppUserOrderController extends Controller
{
    public function members(Request $request){
        try {
            $familyMembers = FamilyMember::with('relation:id,name')
                ->select('id','name','relation_id')
                ->where('app_user_id', $request->user()->id)
                ->get()
                ->map(function($row){
                    return [
                        'id' => $row->id,
                        'name' => $row->name ?? null,
                        'relation_id' => $row->relation->id ?? null,
                        'relation' => $row->relation->name ?? null, 
                    ];
                });
            $lastId = $familyMembers->max('id') ?? 0;

            $selfMember[] =
            [    'id' => $lastId + 1, 
                'name' => $request->user()->name ?? null,
                'relation_id' => null,
                'relation' => 'Self'
            ];
            $data = array_merge($selfMember, $familyMembers->toArray());
            return response()->json(['status' => true, 'message' => 'Members fetched successfully', 'data' => $data],200);
        } catch (\Exception $ex) {
            Log::error('Failed to fetch members',
                ['exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__
            ]);
            return response()->json(['status' => false, 'message' => 'Error fetching members', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function slots(Request $request){
        try {
            $slots = Slot::select('id','slot_time','shift')
                ->where('status','active')
                ->get();
            if(date('d-m-Y') == $request->date){
                $currentTime = Carbon::now();

                $slots = $slots->filter(function ($slot) use ($currentTime) {
                    $slotStart = Carbon::createFromFormat('h:i A', explode(' - ', $slot->slot_time)[0]);
                    return $slotStart->greaterThan($currentTime);
                })->values();
            }
            
            return response(['status'=>true, 'message'=>'Slots fetched successfully!', 'data'=>$slots], 200);
        } catch (\Exception $ex) {
            Log::error('Failed to fetch slots',
                ['exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__
            ]);
            return response()->json(['status' => false, 'message' => 'Error fetching slots', 'error' => 'Internal Server Error'], 500);
        }
    }

    public function index(){
        return "order";
    }

    public function store(){
        return "order";
    }
}
