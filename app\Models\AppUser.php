<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Foundation\Auth\AppUser as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Models\Masters\State;
use App\Models\Masters\City;
use App\Models\Masters\BloodGroup;

class AppUser extends Model
{
    use HasFactory, Notifiable, HasApiTokens;

    protected $fillable = [
        'app_user_id',
        'mobile',
        'otp',
        'password',
        'state_id',
        'city_id',
        'latitude',
        'longitude',
        'name',
        'email',
        'profile_image',
        'gender',
        'blood_group_id',
        'dob',
        'blood_doner_status',
        'address',
    ];

    protected $hidden = [
        'password',
        'otp',
    ];

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function bloodGroup()
    {
        return $this->belongsTo(BloodGroup::class);
    }

    public function getProfileImageUrlAttribute()
    {
        return file_exists(public_path('uploads/app_users/' . $this->profile_image)) && !empty($this->profile_image)
            ? asset('uploads/app_users/' . $this->profile_image) 
            : null;
    }

    protected static function booted(){
        static::created(function ($user) {
            $user->app_user_id = "UID/" . $user->id;
            $user->save();
        });
    }
}
