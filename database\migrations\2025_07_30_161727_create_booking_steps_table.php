<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('booking_steps', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('sequence')->nullable();
            $table->string('title');
            $table->text('description');
            $table->string('type',100)->nullable(); //app-user, collection-center
            $table->enum('status',['active','inactive'])->default('active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('booking_steps');
    }
};
