<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class AuthController extends Controller
{
    public function login(){
        if (auth()->check()) {
            return to_route('dashboard');
        }
        return view('admin.login');
    }

    public function loginSubmit(Request $request){
        $request->validate([
            'email' => 'required|email|exists:users,email',
            'password' => 'required|min:6',
        ]);
        $user = User::where('email', $request->email)->first();
        if($user->status != 'active') {
            return back()->with('error', 'Your account is not active or you do not have permission to access this area.');
        }

        if(Auth::attempt(['email' => $request->email, 'password' => $request->password, 'status' => 'active', 'role_id' => '1'])){ 
            return to_route('dashboard')->with('success','Login Successfully!');
        }else {
            return back()->with('error', 'Your credentials do not match.');
        }
    }

    public function dashboard(){
        return view('admin.dashboard',
            [
                'totalOrders' => 49,
                'orderProgress' => 50,

                'totalRevenue' => 15400,
                'revenueProgress' => 70,

                'totalLabs' => 12,
                'labProgress' => 60,

                'totalUsers' => 86,
                'userProgress' => 40,

                'todayOrders' => 8,
                'todayOrderProgress' => 30,

                'todayReports' => 5,
                'reportProgress' => 80,
            ]
        );
    }

    public function logout(){
        Auth::logout();
        session()->flush();
        return to_route('login')->with('success', 'You have been logged out successfully.');
    }
}
