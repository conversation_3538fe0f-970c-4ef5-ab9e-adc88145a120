<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('test_mappings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('test_id')->constrained('tests')->onDelete('cascade');
            $table->foreignId('lab_id')->constrained('labs')->onDelete('cascade');
            $table->string('lab_test_code',50)->nullable();
            $table->foreignId('test_method_id')->nullable()->constrained('test_methods')->onDelete('set null');
            $table->integer('lab_price')->default(0);
            $table->integer('user_price')->default(0);
            $table->integer('sld_price')->default(0);
            $table->integer('net_price')->default(0);
            $table->enum('status',['enable','disable'])->default('disable');
            $table->timestamps();

            $table->unique(['test_id','lab_id'])->uniue();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('test_mappings');
    }
};
