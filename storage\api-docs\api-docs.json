{"openapi": "3.0.0", "info": {"title": "Your API Title", "description": "This is the API documentation for Your App", "version": "1.0.0"}, "servers": [{"url": "http://127.0.0.1:8000/", "description": "API Server"}], "paths": {"/api/register-login-otp": {"post": {"tags": ["Authentication"], "summary": "Register or login user using OTP", "description": "Validates mobile number and sends a 6-digit OTP for login or registration. If user does not exist, creates a new user.", "operationId": "registerLoginOtp", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["mobile"], "properties": {"mobile": {"description": "User's 10-digit mobile number", "type": "string", "pattern": "^\\d{10}$", "example": "9876543210"}}, "type": "object"}}}}, "responses": {"200": {"description": "OTP sent successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "OTP sent successfully"}, "data": {"properties": {"mobile": {"type": "string", "example": "9876543210"}, "otp": {"type": "string", "example": "123456"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation Error!"}, "error": {"type": "string", "example": "The mobile field is required."}}, "type": "object"}}}}}}}, "/api/categories": {"get": {"tags": ["Categories"], "summary": "Fetch Categories", "description": "Retrieve a list of categories with optional filtering by ID.", "operationId": "f629d8347f04acdbbcc5b37aa568d5ac", "parameters": [{"name": "id", "in": "query", "description": "Filter by category ID", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Category Data"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Electronics"}, "icon": {"type": "string", "example": "fa fa-tv"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/states": {"get": {"tags": ["State Location"], "summary": "Fetch active states", "description": "Returns a list of active states (id and name only).", "operationId": "getStates", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "States fetched successfully!"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Gujarat"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/cities": {"get": {"tags": ["City Location"], "summary": "Fetch active cities", "description": "Returns a list of active cities. Optionally filter by state_id.", "operationId": "getCities", "parameters": [{"name": "state_id", "in": "query", "description": "Filter cities by state ID", "required": false, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Cities fetched successfully!"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 101}, "name": {"type": "string", "example": "Ahmedabad"}, "state_id": {"type": "integer", "example": 1}, "state": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Gujarat"}}, "type": "object"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/organs": {"get": {"tags": ["Oragans"], "summary": "Fetch active organs", "description": "Returns a list of active organs. You can optionally filter by organ ID.", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "query", "description": "Filter by organ ID", "required": false, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Organ fetched successfully!"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Heart"}, "icon": {"type": "string", "example": "heart.png"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/containers": {"get": {"tags": ["Containers"], "summary": "Fetch active containers", "description": "Returns a list of active containers. Optionally filter by container ID.", "operationId": "getContainers", "parameters": [{"name": "id", "in": "query", "description": "Filter by container ID", "required": false, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Container fetched successfully!"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Serum Separator <PERSON>"}, "tube_color": {"type": "string", "example": "Yellow"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/conditions": {"get": {"tags": ["Conditions"], "summary": "Fetch conditions", "description": "Returns a list of conditions. You can optionally filter by ID and status.", "operationId": "getConditions", "parameters": [{"name": "id", "in": "query", "description": "Filter by Condition ID", "required": false, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Condition fetched successfully!"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Diabetes"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/specialities": {"get": {"tags": ["Specialities"], "summary": "Fetch specialities", "description": "Returns a list of specialities. You can optionally filter by ID and status.", "operationId": "getSpecialities", "parameters": [{"name": "id", "in": "query", "description": "Filter by Speciality ID", "required": false, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Speciality fetched successfully!"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Cardiology"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/tests": {"get": {"tags": ["Tests"], "summary": "Fetch tests", "description": "Get a list of diagnostic tests with filters and relationships.", "operationId": "getTests", "parameters": [{"name": "id", "in": "query", "description": "Filter by Test ID", "required": false, "schema": {"type": "integer", "example": 1}}, {"name": "fasting_required", "in": "query", "description": "Filter by fasting requirement (active/inactive)", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"], "example": "active"}}, {"name": "is_package", "in": "query", "description": "Filter by package tests (active/inactive)", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"], "example": "inactive"}}, {"name": "is_popular", "in": "query", "description": "Filter by popular tests (active/inactive)", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"], "example": "active"}}, {"name": "is_special", "in": "query", "description": "Filter by special tests (active/inactive)", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"], "example": "inactive"}}, {"name": "status", "in": "query", "description": "Filter by status (active or inactive)", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"], "example": "active"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Test fetched successfully!"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Lipid Profile"}, "fasting_required": {"type": "string", "enum": ["active", "inactive"], "example": "active"}, "is_package": {"type": "string", "enum": ["active", "inactive"], "example": "inactive"}, "is_popular": {"type": "string", "enum": ["active", "inactive"], "example": "active"}, "is_special": {"type": "string", "enum": ["active", "inactive"], "example": "inactive"}, "organ": {"properties": {"id": {"type": "integer", "example": 2}, "name": {"type": "string", "example": "Heart"}}, "type": "object"}, "category": {"properties": {"id": {"type": "integer", "example": 3}, "name": {"type": "string", "example": "Blood Test"}}, "type": "object"}, "condition": {"properties": {"id": {"type": "integer", "example": 5}, "name": {"type": "string", "example": "Diabetes"}}, "type": "object"}, "speciality": {"properties": {"id": {"type": "integer", "example": 4}, "name": {"type": "string", "example": "Cardiology"}}, "type": "object"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/collection-centers": {"get": {"tags": ["Collection Centers"], "summary": "Get all active collection centers", "description": "Returns a list of active collection centers. You can filter by ID.", "operationId": "getCollectionCenters", "parameters": [{"name": "id", "in": "query", "description": "Filter by Collection Center ID", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Collection Center fetched successfully!"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "XYZ Lab"}, "status": {"type": "string", "example": "active"}, "user": {"properties": {"id": {"type": "integer", "example": 5}, "name": {"type": "string", "example": "<PERSON>"}}, "type": "object"}, "state": {"properties": {"id": {"type": "integer", "example": 10}, "name": {"type": "string", "example": "Maharashtra"}}, "type": "object"}, "city": {"properties": {"id": {"type": "integer", "example": 22}, "name": {"type": "string", "example": "Pune"}}, "type": "object"}, "timings": {"type": "array", "items": {"properties": {"key": {"type": "integer", "example": 1}, "day": {"type": "string", "example": "Monday"}, "timing": {"type": "string", "example": "10:00:00 - 18:00:00"}}, "type": "object"}}}, "type": "object"}}}, "type": "object"}}}}, "500": {"description": "Internal Server Error"}}}}}, "tags": [{"name": "Authentication", "description": "Authentication"}, {"name": "Categories", "description": "Categories"}, {"name": "State Location", "description": "State Location"}, {"name": "City Location", "description": "City Location"}, {"name": "Oragans", "description": "Oragans"}, {"name": "Containers", "description": "Containers"}, {"name": "Conditions", "description": "Conditions"}, {"name": "Specialities", "description": "Specialities"}, {"name": "Tests", "description": "Tests"}, {"name": "Collection Centers", "description": "Collection Centers"}]}