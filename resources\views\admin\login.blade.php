<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{{ config('constant.app_name') }} - Login</title>

        {{-- bootstrap css --}}
        <link rel="stylesheet" href="{{ asset('assets/admin/bootstrap/bootstrap.min.css') }}">
        {{-- font awesome --}}
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        {{-- jquery --}}
        <script src="{{ asset('assets/jquery/jquery.min.js') }}"></script>
        
        <link rel="icon" href="{{ asset('logo-img.png') }}" type="image/png">


            
        <style>
            :root {
                --primary-color: #4e73df;
                --secondary-color: #f8f9fc;
                --accent-color: #4e73df;
                --text-color: #5a5c69;
                --shadow-color: rgba(0, 0, 0, 0.1);
            }
            
            body {
                /* background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); */
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .login-container {
                width: 100%;
                max-width: 450px;
                padding: 00px;
            }
            
            .login-card {
                /* background-color: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) */
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 15px 30px var(--shadow-color);
                overflow: hidden;
                transition: all 0.3s ease;
            }
            
            .login-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px var(--shadow-color);
            }
            
            .login-header {
                background: var(--primary-color);
                color: white;
                padding: 20px;
                text-align: center;
                font-weight: 600;
                font-size: 1.5rem;
                border-radius: 10px 10px 0 0;
            }
            
            .login-body {
                padding: 30px;
            }
            
            .form-control {
                border-radius: 5px;
                padding: 12px;
                border: 1px solid #e2e8f0;
                transition: all 0.3s ease;
            }
            
            .form-control:focus {
                border-color: var(--accent-color);
                box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
            }
            
            .form-label {
                font-weight: 500;
                color: var(--text-color);
                margin-bottom: 8px;
            }
            
            .btn-login {
                background-color: var(--primary-color);
                border: none;
                border-radius: 5px;
                color: white;
                font-weight: 600;
                padding: 12px;
                width: 100%;
                transition: all 0.3s ease;
            }
            
            .btn-login:hover {
                background-color: #3a5ccc;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(78, 115, 223, 0.4);
            }
            
            .form-floating {
                position: relative;
                margin-bottom: 20px;
            }
            
            .form-floating input {
                height: 60px;
                padding-top: 25px;
                padding-bottom: 10px;
            }
            
            .form-floating label {
                padding: 20px 12px;
            }
            
            .form-floating input:focus ~ label,
            .form-floating input:not(:placeholder-shown) ~ label {
                opacity: 0.65;
                transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            }
            
            .brand-logo {
                text-align: center;
                margin-bottom: 20px;
            }
            
            .brand-logo img {
                height: 130px;
            }
            
            .login-footer {
                text-align: center;
                padding: 15px;
                color: var(--text-color);
                font-size: 0.9rem;
            }
        </style>

        <script src="{{ asset('assets/sweetalert/sweetalert2.min.js') }}"></script>
    </head>
    <body>

        {{-- login form --}}
        <div class="login-container">
            <div class="brand-logo">
                <img src="{{ asset('logo-img.png') }}" width="200px;" height="200px;" alt="Logo Image">
            </div>
            
            <div class="login-card">
                <div class="login-header">
                    Admin Sign In
                </div>
                
                <div class="login-body">
                    <x-alert />
                    
                    <form action="{{ route('loginSubmit') }}" method="POST">
                        @csrf
                        
                        <div class="form-floating mb-4">
                            <input type="email" class="form-control" id="email" name="email" placeholder="Email address" value="{{ old('email') }}" required>
                            <label for="email">Email address</label>
                        </div>
                        
                        <div class="form-floating mb-4">
                            <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                            <label for="password">Password</label>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button class="btn btn-login" type="submit">
                                <i class="fas fa-sign-in-alt me-2"></i> Sign In
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="login-footer">
                    &copy; {{ date('Y') }} <a href="{{ config('constant.developed_by_url') }}"> {{ config('constant.developed_by_name') }} </a>. All rights reserved.
                </div>
            </div>
        </div>

        {{-- bootstrap js --}}
        <script src="{{ asset('assets/admin/bootstrap/bootstrap.min.js') }}"></script>
    </body>
</html>