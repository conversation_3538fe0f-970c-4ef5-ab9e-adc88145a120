<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Opinion extends Model
{
    protected $fillable = [
        'app_user_id',
        'type',
        'message',
        'rating',
        'app_visibility',
        'reply_message',
        'status'
    ];

    public function appUser(){
        return $this->belongsTo(AppUser::class);
    }

    public function getCreatedAtAttribute($value){
        return $this->created_at = date('d-m-Y', strtotime($value));
    }
}
