<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (DB::table('roles')->count() > 0) {
            return; // Skip seeding if roles already exist
        }
        \DB::table('roles')->insert([
            ['name' => 'Admin', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Manager', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Data Entry', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Collection Center', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'User', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Phlebo', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Doctor', 'status' => 'active', 'created_at' => now(), 'updated_at' => now()],
        ]);
    }
}
