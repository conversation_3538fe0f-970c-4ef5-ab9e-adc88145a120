<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('opinions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('app_user_id')->constrained('app_users')->onsDelete('cascade');
            $table->enum('type',['FEEDBACK','SUGGESTION','COMPLAIN']);
            $table->text('message');
            $table->enum('rating',['0','1','2','3','4','5'])->default('0');
            $table->enum('app_visibility',['0','1'])->default('0');
            $table->text('reply_message')->nullable();
            $table->enum('status',['unread','read'])->default('unread');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('opinions');
    }
};
