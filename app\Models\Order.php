<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    protected $fillable = [
        'order_no',
        'app_user_id',
        'collection_center_id',

        'sub_total',
        'grand_total',

        'coupon_id',
        'user_discount_amt',
        'center_discount_amt',

        'reward_point_used',
        'reward_amt_used',

        'collection_date',
        'slot_id',

        'booking_type',
        'app_user_address_id',

        'payment_type',
        'payment_id',
        'payment_status',

        'booking_status',
        'cancelled_at',
        'cancel_reason_id',
        'cancel_remark',

        'receipt_no',
    ];


    public function appUser()
    {
        return $this->belongsTo(AppUser::class);
    }

    public function collectionCenter()
    {
        return $this->belongsTo(CollectionCenter::class);
    }

    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }

    public function slot()
    {
        return $this->belongsTo(Slot::class);
    }

    public function appUserAddress()
    {
        return $this->belongsTo(AppUserAddress::class);
    }

    public function cancelReason()
    {
        return $this->belongsTo(CancelReason::class);
    }
}
